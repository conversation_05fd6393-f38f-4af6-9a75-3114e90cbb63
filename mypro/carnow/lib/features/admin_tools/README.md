# الأدوات المالية الإدارية - Flutter UI

## نظرة عامة

تم تطوير واجهة مستخدم شاملة للأدوات المالية الإدارية في تطبيق CarNow باستخدام Flutter. يوفر هذا النظام واجهة متقدمة للمديرين لإدارة العمليات المالية ومراقبة النظام.

## المكونات الرئيسية

### 1. النماذج (Models)
- **AdminFinancialModels**: نماذج البيانات مع دعم Freezed و JSON serialization
- **Enums**: تعدادات لأنواع الإجراءات، مستويات التنبيهات، وأنواع التقارير
- **Extensions**: امتدادات لعرض النصوص والألوان

### 2. مقدمو الخدمة (Providers)
- **AdminFinancialService**: خدمة التفاعل مع API الباك اند
- **Dashboard Providers**: مقدمو خدمة لوحة التحكم والملخص المالي
- **Wallet Providers**: مقدمو خدمة إدارة المحافظ
- **Alert Providers**: مقدمو خدمة إدارة التنبيهات
- **Report Providers**: مقدمو خدمة التقارير والتحليلات

### 3. الشاشات (Screens)
- **AdminFinancialDashboardScreen**: لوحة التحكم الرئيسية
- **AdminWalletManagementScreen**: شاشة إدارة المحافظ

### 4. الويدجت (Widgets)
- **AdminDashboardCard**: بطاقات عرض المعلومات
- **AdminFinancialChart**: الرسوم البيانية المالية
- **AdminAlertsSummary**: ملخص التنبيهات
- **AdminRecentActions**: الأنشطة الحديثة
- **AdminSystemHealth**: صحة النظام

## الميزات الرئيسية

### 🎛️ لوحة التحكم المالية
- عرض الملخص المالي العام
- مراقبة المعاملات والأنشطة
- الرسوم البيانية التفاعلية
- إدارة الموافقات المعلقة

### 💰 إدارة المحافظ
- عرض قائمة شاملة بالمحافظ
- تفاصيل مفصلة لكل محفظة
- إحصائيات المعاملات
- إجراءات الإدارة (قفل، تعديل الرصيد، إلخ)

### 🚨 إدارة التنبيهات
- عرض التنبيهات حسب الأولوية
- حل التنبيهات مع التوثيق
- إحصائيات معدل الحل

### 📊 النشاطات والتدقيق
- سجل الأنشطة الإدارية
- تفاصيل المعاملات المشبوهة
- تتبع إجراءات المديرين

### 🔧 صحة النظام
- مراقبة حالة الخدمات
- تنبيهات الأعطال
- مؤشرات الأداء

## كيفية الاستخدام

### 1. الاستيراد
```dart
import 'package:carnow/features/admin_tools/admin_tools_index.dart';
```

### 2. إعداد التوجيه
```dart
// في ملف التوجيه الرئيسي
GoRoute(
  path: '/admin/financial-dashboard',
  builder: (context, state) => const AdminFinancialDashboardScreen(),
),
GoRoute(
  path: '/admin/wallet-management',
  builder: (context, state) => const AdminWalletManagementScreen(),
),
```

### 3. استخدام Providers
```dart
class MyAdminScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardAsync = ref.watch(adminDashboardProvider);
    
    return dashboardAsync.when(
      data: (dashboard) => /* بناء الواجهة */,
      loading: () => const LoadingWidget(),
      error: (error, stack) => AppErrorWidget(/* معالجة الخطأ */),
    );
  }
}
```

### 4. استخدام الويدجت
```dart
AdminDashboardCard(
  title: 'إجمالي الأرصدة',
  value: '150,000 ر.س',
  icon: Icons.account_balance_wallet,
  color: AppColors.success,
  onTap: () => Navigator.push(/* التنقل */),
)
```

## إعداد API

### URL الأساسي
```dart
// في AdminFinancialService
baseUrl: 'http://localhost:8000/api/v1/admin-tools'
```

### المصادقة
```dart
headers: {
  'Authorization': 'Bearer ${supabaseToken}',
  'Content-Type': 'application/json',
}
```

## الأمان والصلاحيات

### 🔒 مستويات الأمان
- مصادقة الجلسة مطلوبة
- فحص صلاحيات المدير
- تسجيل جميع الإجراءات
- تشفير البيانات الحساسة

### 👥 أدوار المديرين
- **Financial Analyst**: عرض التقارير فقط
- **Financial Manager**: إدارة محدودة
- **Operations Manager**: إدارة شاملة
- **Super Admin**: صلاحيات كاملة

## معالجة الأخطاء

### 🛠️ استراتيجيات المعالجة
- عرض رسائل خطأ واضحة
- إمكانية إعادة المحاولة
- تسجيل الأخطاء للتتبع
- حالات الطوارئ الاحتياطية

### 📱 تجربة المستخدم
- Loading states مناسبة
- رسائل تأكيد الإجراءات
- ملاحظات فورية للمستخدم
- تصميم متجاوب

## التطوير المستقبلي

### 🚀 ميزات مخططة
- تحليلات متقدمة بالذكاء الاصطناعي
- تنبيهات في الوقت الفعلي
- تقارير قابلة للتخصيص
- إدارة المخاطر التلقائية
- واجهة برمجة تطبيقات محسنة

### 🔧 تحسينات تقنية
- تحسين الأداء
- دعم الوضع المظلم
- ترجمة متعددة اللغات
- دعم الأجهزة اللوحية

## الاختبار

### 🧪 أنواع الاختبارات
- Unit Tests للنماذج والخدمات
- Widget Tests للمكونات
- Integration Tests للتدفقات الكاملة
- Performance Tests للأداء

### 📋 تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/features/admin_tools/
```

## المساهمة

### 📝 معايير التطوير
- اتباع معايير Flutter و Dart
- استخدام Riverpod للإدارة
- تطبيق مبادئ Clean Architecture
- توثيق شامل للكود

### 🔄 عملية المراجعة
1. إنشاء branch جديد
2. تطوير الميزة
3. كتابة الاختبارات
4. مراجعة الكود
5. دمج التحديثات

---

**ملاحظة**: هذا النظام جزء من منصة CarNow المتكاملة ويتطلب إعداد الباك اند المناسب للعمل بشكل كامل. 