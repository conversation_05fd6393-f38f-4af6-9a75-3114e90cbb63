import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:http/http.dart' as http;
import 'package:riverpod/riverpod.dart';
import '../models/admin_financial_models.dart';
import '../../../core/config/backend_config.dart';

part 'admin_approval_providers.g.dart';

/// Base URL for the Go financial backend (Carnow).
/// Uses smart backend service for automatic URL selection.
String _financialBackendBaseUrl(Ref ref) {
  // Use smart backend service to get the correct URL (localhost or ******** for emulator)
  return CarnowBackendConfig.apiBaseUrl;
}

/// =============================================
/// PROVIDER: Pending Approvals
/// =============================================
@riverpod
Future<List<TransactionAnalysis>> pendingApprovals(Ref ref) async {
  final baseUrl = _financialBackendBaseUrl(ref);
  final uri = Uri.parse('$baseUrl/admin/transactions/pending');
  final token = ref.watch(authTokenProvider);

  final response = await http.get(
    uri,
    headers: {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    },
  );

  if (response.statusCode != 200) {
    throw Exception('Failed to load pending approvals (${response.statusCode})');
  }

  final map = jsonDecode(response.body) as Map<String, dynamic>;
  final List<dynamic> data = map['data'] ?? [];
  return data
      .map((e) => TransactionAnalysis.fromJson(e as Map<String, dynamic>))
      .toList();
}

/// Parameters to approve/reject a transaction
class ApprovalParams {
  const ApprovalParams({required this.transactionId, required this.approve, this.reason});

  final String transactionId;
  final bool approve;
  final String? reason;

  Map<String, dynamic> toJson() => {
        'transaction_id': transactionId,
        'action': approve ? 'approve' : 'reject',
        if (reason != null) 'reason': reason,
      };
}

/// =============================================
/// PROVIDER: Approve / Reject Transaction
/// Returns true on success.
/// =============================================
@riverpod
Future<void> approveTransaction(Ref ref, ApprovalParams params) async {
  final baseUrl = _financialBackendBaseUrl(ref);
  final uri = Uri.parse('$baseUrl/admin/transactions/approval');
  final token = ref.watch(authTokenProvider);

  final response = await http.post(
    uri,
    headers: {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    },
    body: jsonEncode(params.toJson()),
  );

  if (response.statusCode != 200) {
    final err = jsonDecode(response.body) as Map<String, dynamic>;
    throw Exception(err['error'] ?? 'Approval failed');
  }

  // Invalidate pending list so UI refreshes
  ref.invalidate(pendingApprovalsProvider);
}

// after imports define token provider
const _kDummyToken = null;
final authTokenProvider = Provider<String?>((ref) => _kDummyToken);
