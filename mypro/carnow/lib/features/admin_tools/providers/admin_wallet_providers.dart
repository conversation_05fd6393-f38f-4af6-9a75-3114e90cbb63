import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:dio/dio.dart';
import 'package:logging/logging.dart';

import '../../../core/providers/dio_provider.dart';
import '../../../core/config/backend_config.dart';
import '../../../core/errors/app_error.dart';
import '../models/admin_financial_models.dart';

part 'admin_wallet_providers.g.dart';

final _logger = Logger('AdminWalletProviders');

/// ================================================
/// ADMIN WALLET SERVICE (SIMPLE HTTP CALLS)
/// ================================================

/// خدمة إدارة المحافظ الإدارية (بسيطة)
@riverpod
AdminWalletService adminWalletService(Ref ref) {
  final dio = ref.watch(dioProvider);
  return AdminWalletService(dio);
}

class AdminWalletService {
  AdminWalletService(this._dio);
  
  final Dio _dio;

  /// جلب جميع المحافظ
  Future<List<WalletOverview>> getAllWallets({
    int page = 1,
    int limit = 50,
    String? search,
    String? status,
    String? verificationLevel,
    bool? isFlagged,
  }) async {
    try {
      final response = await _dio.get(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets',
        queryParameters: {
          'page': page,
          'limit': limit,
          if (search != null) 'search': search,
          if (status != null) 'status': status,
          if (verificationLevel != null) 
            'verification_level': verificationLevel,
          if (isFlagged != null) 'is_flagged': isFlagged,
        },
      );
      
      final data = response.data as Map<String, dynamic>;
      final walletsData = data['data'] as List;
      
      return walletsData
          .map((json) => WalletOverview.fromJson(json))
          .toList();
    } catch (e) {
      _logger.severe('Error fetching wallets: $e');
      throw AppError.network(
        message: 'فشل في جلب المحافظ',
        originalError: e,
      );
    }
  }

  /// جلب تفاصيل محفظة محددة
  Future<WalletOverview?> getWalletDetails(String walletId) async {
    try {
      final response = await _dio.get(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets/$walletId',
      );
      
      return WalletOverview.fromJson(response.data);
    } catch (e) {
      _logger.severe('Error fetching wallet details: $e');
      throw AppError.network(
        message: 'فشل في جلب تفاصيل المحفظة',
        originalError: e,
      );
    }
  }

  /// البحث عن المحافظ
  Future<List<WalletOverview>> searchWallets(String query) async {
    return getAllWallets(search: query);
  }

  /// تعديل رصيد المحفظة
  Future<WalletAdjustmentResponse> adjustWalletBalance(
    WalletAdjustmentRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets/${request.walletId}/adjust',
        data: request.toJson(),
      );
      
      return WalletAdjustmentResponse.fromJson(response.data);
    } catch (e) {
      _logger.severe('Error adjusting wallet balance: $e');
      throw AppError.network(
        message: 'فشل في تعديل رصيد المحفظة',
        originalError: e,
      );
    }
  }

  /// تجميد المحفظة
  Future<void> freezeWallet(String walletId, String reason, String adminId) async {
    try {
      await _dio.post(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets/$walletId/freeze',
        data: {
          'reason': reason,
          'admin_id': adminId,
        },
      );
    } catch (e) {
      _logger.severe('Error freezing wallet: $e');
      throw AppError.network(
        message: 'فشل في تجميد المحفظة',
        originalError: e,
      );
    }
  }

  /// إلغاء تجميد المحفظة
  Future<void> unfreezeWallet(String walletId, String reason, String adminId) async {
    try {
      await _dio.post(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets/$walletId/unfreeze',
        data: {
          'reason': reason,
          'admin_id': adminId,
        },
      );
    } catch (e) {
      _logger.severe('Error unfreezing wallet: $e');
      throw AppError.network(
        message: 'فشل في إلغاء تجميد المحفظة',
        originalError: e,
      );
    }
  }

  /// تعديل حدود المحفظة
  Future<void> updateWalletLimits(
    String walletId,
    double dailyLimit,
    double monthlyLimit,
    String adminId,
  ) async {
    try {
      await _dio.post(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/wallets/$walletId/limits',
        data: {
          'daily_limit': dailyLimit,
          'monthly_limit': monthlyLimit,
          'admin_id': adminId,
        },
      );
    } catch (e) {
      _logger.severe('Error updating wallet limits: $e');
      throw AppError.network(
        message: 'فشل في تعديل حدود المحفظة',
        originalError: e,
      );
    }
  }

  /// جلب التنبيهات المالية
  Future<List<FinancialAlert>> getAlerts({
    int page = 1,
    int limit = 50,
    AlertSeverity? severity,
    bool? isResolved,
    String? alertType,
  }) async {
    try {
      final response = await _dio.get(
        '${CarnowBackendConfig.baseUrl}/api/v1/admin/alerts',
        queryParameters: {
          'page': page,
          'limit': limit,
          if (severity != null) 'severity': severity.name,
          if (isResolved != null) 'is_resolved': isResolved,
          if (alertType != null) 'alert_type': alertType,
        },
      );
      
      final data = response.data as Map<String, dynamic>;
      final alertsData = data['data'] as List;
      
      return alertsData
          .map((json) => FinancialAlert.fromJson(json))
          .toList();
    } catch (e) {
      _logger.severe('Error fetching alerts: $e');
      throw AppError.network(
        message: 'فشل في جلب التنبيهات',
        originalError: e,
      );
    }
  }

  /// باقي الطرق بنفس النمط - مكالمات HTTP بسيطة للـ Go backend
  // ... مكالمات HTTP بسيطة أخرى ...
}

/// ================================================
/// WALLET MANAGEMENT PROVIDERS (SIMPLE)
/// ================================================

/// Provider لجلب جميع المحافظ
@riverpod
class AdminWalletsNotifier extends _$AdminWalletsNotifier {
  @override
  Future<List<WalletOverview>> build() async {
    final service = ref.watch(adminWalletServiceProvider);
    try {
      return service.getAllWallets();
    } catch (e) {
      _logger.severe('Error fetching wallets: $e');
      throw AppError.network(
        message: 'فشل في جلب المحافظ',
        originalError: e,
      );
    }
  }

  /// جلب المحافظ مع تصفية
  Future<List<WalletOverview>> fetchWallets({
    int page = 1,
    int limit = 50,
    String? search,
    String? status,
    String? verificationLevel,
    bool? isFlagged,
  }) async {
    final service = ref.read(adminWalletServiceProvider);
    try {
      final wallets = await service.getAllWallets(
        page: page,
        limit: limit,
        search: search,
        status: status,
        verificationLevel: verificationLevel,
        isFlagged: isFlagged,
      );
      
      state = AsyncValue.data(wallets);
      return wallets;
    } catch (e) {
      _logger.severe('Error fetching wallets with filter: $e');
      state = AsyncValue.error(e, StackTrace.current);
      throw AppError.network(
        message: 'فشل في جلب المحافظ',
        originalError: e,
      );
    }
  }

  /// البحث عن المحافظ
  Future<List<WalletOverview>> searchWallets(String query) async {
    final service = ref.read(adminWalletServiceProvider);
    try {
      return service.searchWallets(query);
    } catch (e) {
      _logger.severe('Error searching wallets: $e');
      throw AppError.network(
        message: 'فشل في البحث عن المحافظ',
        originalError: e,
      );
    }
  }

  /// تجديد قائمة المحافظ
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final service = ref.read(adminWalletServiceProvider);
      return service.getAllWallets();
    });
  }
}

/// Provider لتفاصيل محفظة محددة
@riverpod
class WalletDetailsNotifier extends _$WalletDetailsNotifier {
  @override
  Future<WalletOverview?> build(String walletId) async {
    final service = ref.watch(adminWalletServiceProvider);
    try {
      return service.getWalletDetails(walletId);
    } catch (e) {
      _logger.severe('Error fetching wallet details: $e');
      return null;
    }
  }

  /// تجديد تفاصيل المحفظة
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final service = ref.read(adminWalletServiceProvider);
      return service.getWalletDetails(walletId);
    });
  }
}

/// ================================================
/// UTILITY PROVIDERS (SIMPLE)
/// ================================================

/// Provider لحالة البحث
@riverpod
class SearchStateNotifier extends _$SearchStateNotifier {
  @override
  String build() => '';

  void updateSearch(String query) => state = query;
  void clearSearch() => state = '';
}

/// Provider لفلاتر المحافظ
@riverpod
class WalletFiltersNotifier extends _$WalletFiltersNotifier {
  @override
  WalletFilters build() => const WalletFilters();

  void updateFilters(WalletFilters filters) => state = filters;
  void clearFilters() => state = const WalletFilters();
}

/// نموذج فلاتر المحافظ
class WalletFilters {
  const WalletFilters({
    this.status,
    this.verificationLevel,
    this.isFlagged,
    this.minBalance,
    this.maxBalance,
  });

  final String? status;
  final String? verificationLevel;
  final bool? isFlagged;
  final double? minBalance;
  final double? maxBalance;

  WalletFilters copyWith({
    String? status,
    String? verificationLevel,
    bool? isFlagged,
    double? minBalance,
    double? maxBalance,
  }) {
    return WalletFilters(
      status: status ?? this.status,
      verificationLevel: verificationLevel ?? this.verificationLevel,
      isFlagged: isFlagged ?? this.isFlagged,
      minBalance: minBalance ?? this.minBalance,
      maxBalance: maxBalance ?? this.maxBalance,
    );
  }
}