// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_wallet_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminWalletServiceHash() =>
    r'21a90a8a96a553b6b2b2db2936dfde710347ada6';

/// ================================================
/// ADMIN WALLET SERVICE (SIMPLE HTTP CALLS)
/// ================================================
/// خدمة إدارة المحافظ الإدارية (بسيطة)
///
/// Copied from [adminWalletService].
@ProviderFor(adminWalletService)
final adminWalletServiceProvider =
    AutoDisposeProvider<AdminWalletService>.internal(
      adminWalletService,
      name: r'adminWalletServiceProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminWalletServiceHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminWalletServiceRef = AutoDisposeProviderRef<AdminWalletService>;
String _$adminWalletsNotifierHash() =>
    r'f1f5b9bad76ad0791e47e3b5d08f8064588428b1';

/// ================================================
/// WALLET MANAGEMENT PROVIDERS (SIMPLE)
/// ================================================
/// Provider لجلب جميع المحافظ
///
/// Copied from [AdminWalletsNotifier].
@ProviderFor(AdminWalletsNotifier)
final adminWalletsNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      AdminWalletsNotifier,
      List<WalletOverview>
    >.internal(
      AdminWalletsNotifier.new,
      name: r'adminWalletsNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$adminWalletsNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AdminWalletsNotifier = AutoDisposeAsyncNotifier<List<WalletOverview>>;
String _$walletDetailsNotifierHash() =>
    r'8e78a04c82aa3100b2fcd33c5bb6fd0dccbecd5d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$WalletDetailsNotifier
    extends BuildlessAutoDisposeAsyncNotifier<WalletOverview?> {
  late final String walletId;

  FutureOr<WalletOverview?> build(String walletId);
}

/// Provider لتفاصيل محفظة محددة
///
/// Copied from [WalletDetailsNotifier].
@ProviderFor(WalletDetailsNotifier)
const walletDetailsNotifierProvider = WalletDetailsNotifierFamily();

/// Provider لتفاصيل محفظة محددة
///
/// Copied from [WalletDetailsNotifier].
class WalletDetailsNotifierFamily extends Family<AsyncValue<WalletOverview?>> {
  /// Provider لتفاصيل محفظة محددة
  ///
  /// Copied from [WalletDetailsNotifier].
  const WalletDetailsNotifierFamily();

  /// Provider لتفاصيل محفظة محددة
  ///
  /// Copied from [WalletDetailsNotifier].
  WalletDetailsNotifierProvider call(String walletId) {
    return WalletDetailsNotifierProvider(walletId);
  }

  @override
  WalletDetailsNotifierProvider getProviderOverride(
    covariant WalletDetailsNotifierProvider provider,
  ) {
    return call(provider.walletId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'walletDetailsNotifierProvider';
}

/// Provider لتفاصيل محفظة محددة
///
/// Copied from [WalletDetailsNotifier].
class WalletDetailsNotifierProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          WalletDetailsNotifier,
          WalletOverview?
        > {
  /// Provider لتفاصيل محفظة محددة
  ///
  /// Copied from [WalletDetailsNotifier].
  WalletDetailsNotifierProvider(String walletId)
    : this._internal(
        () => WalletDetailsNotifier()..walletId = walletId,
        from: walletDetailsNotifierProvider,
        name: r'walletDetailsNotifierProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$walletDetailsNotifierHash,
        dependencies: WalletDetailsNotifierFamily._dependencies,
        allTransitiveDependencies:
            WalletDetailsNotifierFamily._allTransitiveDependencies,
        walletId: walletId,
      );

  WalletDetailsNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.walletId,
  }) : super.internal();

  final String walletId;

  @override
  FutureOr<WalletOverview?> runNotifierBuild(
    covariant WalletDetailsNotifier notifier,
  ) {
    return notifier.build(walletId);
  }

  @override
  Override overrideWith(WalletDetailsNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: WalletDetailsNotifierProvider._internal(
        () => create()..walletId = walletId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        walletId: walletId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    WalletDetailsNotifier,
    WalletOverview?
  >
  createElement() {
    return _WalletDetailsNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WalletDetailsNotifierProvider && other.walletId == walletId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, walletId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WalletDetailsNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<WalletOverview?> {
  /// The parameter `walletId` of this provider.
  String get walletId;
}

class _WalletDetailsNotifierProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          WalletDetailsNotifier,
          WalletOverview?
        >
    with WalletDetailsNotifierRef {
  _WalletDetailsNotifierProviderElement(super.provider);

  @override
  String get walletId => (origin as WalletDetailsNotifierProvider).walletId;
}

String _$searchStateNotifierHash() =>
    r'b27d087b0faa16cf88a332df0ab4962f3157c5f7';

/// ================================================
/// UTILITY PROVIDERS (SIMPLE)
/// ================================================
/// Provider لحالة البحث
///
/// Copied from [SearchStateNotifier].
@ProviderFor(SearchStateNotifier)
final searchStateNotifierProvider =
    AutoDisposeNotifierProvider<SearchStateNotifier, String>.internal(
      SearchStateNotifier.new,
      name: r'searchStateNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$searchStateNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$SearchStateNotifier = AutoDisposeNotifier<String>;
String _$walletFiltersNotifierHash() =>
    r'7347edb6fbcefd8f012730ab922ea88bdb12591b';

/// Provider لفلاتر المحافظ
///
/// Copied from [WalletFiltersNotifier].
@ProviderFor(WalletFiltersNotifier)
final walletFiltersNotifierProvider =
    AutoDisposeNotifierProvider<WalletFiltersNotifier, WalletFilters>.internal(
      WalletFiltersNotifier.new,
      name: r'walletFiltersNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$walletFiltersNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$WalletFiltersNotifier = AutoDisposeNotifier<WalletFilters>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
