// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_approval_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pendingApprovalsHash() => r'e4752246e473d50f328e8c81212b2892be63b049';

/// =============================================
/// PROVIDER: Pending Approvals
/// =============================================
///
/// Copied from [pendingApprovals].
@ProviderFor(pendingApprovals)
final pendingApprovalsProvider =
    AutoDisposeFutureProvider<List<TransactionAnalysis>>.internal(
      pendingApprovals,
      name: r'pendingApprovalsProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$pendingApprovalsHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PendingApprovalsRef =
    AutoDisposeFutureProviderRef<List<TransactionAnalysis>>;
String _$approveTransactionHash() =>
    r'0bda6536565c1c9db0e4e31262a3fc44bb7dbe55';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// =============================================
/// PROVIDER: Approve / Reject Transaction
/// Returns true on success.
/// =============================================
///
/// Copied from [approveTransaction].
@ProviderFor(approveTransaction)
const approveTransactionProvider = ApproveTransactionFamily();

/// =============================================
/// PROVIDER: Approve / Reject Transaction
/// Returns true on success.
/// =============================================
///
/// Copied from [approveTransaction].
class ApproveTransactionFamily extends Family<AsyncValue<void>> {
  /// =============================================
  /// PROVIDER: Approve / Reject Transaction
  /// Returns true on success.
  /// =============================================
  ///
  /// Copied from [approveTransaction].
  const ApproveTransactionFamily();

  /// =============================================
  /// PROVIDER: Approve / Reject Transaction
  /// Returns true on success.
  /// =============================================
  ///
  /// Copied from [approveTransaction].
  ApproveTransactionProvider call(ApprovalParams params) {
    return ApproveTransactionProvider(params);
  }

  @override
  ApproveTransactionProvider getProviderOverride(
    covariant ApproveTransactionProvider provider,
  ) {
    return call(provider.params);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'approveTransactionProvider';
}

/// =============================================
/// PROVIDER: Approve / Reject Transaction
/// Returns true on success.
/// =============================================
///
/// Copied from [approveTransaction].
class ApproveTransactionProvider extends AutoDisposeFutureProvider<void> {
  /// =============================================
  /// PROVIDER: Approve / Reject Transaction
  /// Returns true on success.
  /// =============================================
  ///
  /// Copied from [approveTransaction].
  ApproveTransactionProvider(ApprovalParams params)
    : this._internal(
        (ref) => approveTransaction(ref as ApproveTransactionRef, params),
        from: approveTransactionProvider,
        name: r'approveTransactionProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$approveTransactionHash,
        dependencies: ApproveTransactionFamily._dependencies,
        allTransitiveDependencies:
            ApproveTransactionFamily._allTransitiveDependencies,
        params: params,
      );

  ApproveTransactionProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.params,
  }) : super.internal();

  final ApprovalParams params;

  @override
  Override overrideWith(
    FutureOr<void> Function(ApproveTransactionRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ApproveTransactionProvider._internal(
        (ref) => create(ref as ApproveTransactionRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        params: params,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<void> createElement() {
    return _ApproveTransactionProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ApproveTransactionProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ApproveTransactionRef on AutoDisposeFutureProviderRef<void> {
  /// The parameter `params` of this provider.
  ApprovalParams get params;
}

class _ApproveTransactionProviderElement
    extends AutoDisposeFutureProviderElement<void>
    with ApproveTransactionRef {
  _ApproveTransactionProviderElement(super.provider);

  @override
  ApprovalParams get params => (origin as ApproveTransactionProvider).params;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
