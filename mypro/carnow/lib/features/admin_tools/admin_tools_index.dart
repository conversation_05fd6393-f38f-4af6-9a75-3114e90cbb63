// ================================================
// ADMIN TOOLS - FINANCIAL MANAGEMENT INDEX
// ================================================
// This file exports all admin financial tools components
// for easy import and organization

// Models
export 'models/admin_financial_models.dart';

// Core Services
export '../../../core/services/performance_cache_service.dart';

// Providers
export 'providers/admin_financial_providers.dart' hide carnowSupabaseClient, carnowSupabaseClientProvider, CarnowSupabaseClientRef;
export 'providers/admin_wallet_providers.dart' hide WalletFilters, WalletFiltersNotifier, walletFiltersNotifierProvider;
export 'providers/optimized_admin_wallet_providers.dart';

// Screens
export 'screens/admin_financial_dashboard_screen.dart';
export 'screens/admin_wallet_management_screen.dart';
export 'screens/optimized_admin_wallet_screen.dart';
export 'screens/pending_approvals_screen.dart';

// Widgets
export 'widgets/admin_dashboard_card.dart';
export 'widgets/admin_financial_chart.dart';
export 'widgets/admin_alerts_summary.dart';
export 'widgets/admin_recent_actions.dart';
export 'widgets/admin_system_health.dart';
export 'widgets/approval_card.dart';
export 'widgets/risk_badge.dart';
export 'widgets/wallet_details_card.dart';
export 'widgets/wallet_operations_dialog.dart';
export 'widgets/wallet_search_bar.dart';
export 'widgets/wallet_filters_dialog.dart';

// Optimized Widgets
export 'widgets/optimized_wallet_card.dart';
export 'widgets/optimized_search_bar.dart';
export 'widgets/wallet_filters_widget.dart';
