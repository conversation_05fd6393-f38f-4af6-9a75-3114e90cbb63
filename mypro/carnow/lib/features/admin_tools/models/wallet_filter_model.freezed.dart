// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WalletFilterModel {

/// Filter by wallet status
 String? get status;/// Filter by verification status
 bool? get isVerified;/// Filter by flagged status
 bool? get isFlagged;/// Minimum balance filter
 double? get minBalance;/// Maximum balance filter
 double? get maxBalance;/// Start date filter
 DateTime? get startDate;/// End date filter
 DateTime? get endDate;
/// Create a copy of WalletFilterModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WalletFilterModelCopyWith<WalletFilterModel> get copyWith => _$WalletFilterModelCopyWithImpl<WalletFilterModel>(this as WalletFilterModel, _$identity);

  /// Serializes this WalletFilterModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WalletFilterModel&&(identical(other.status, status) || other.status == status)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.minBalance, minBalance) || other.minBalance == minBalance)&&(identical(other.maxBalance, maxBalance) || other.maxBalance == maxBalance)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,isVerified,isFlagged,minBalance,maxBalance,startDate,endDate);

@override
String toString() {
  return 'WalletFilterModel(status: $status, isVerified: $isVerified, isFlagged: $isFlagged, minBalance: $minBalance, maxBalance: $maxBalance, startDate: $startDate, endDate: $endDate)';
}


}

/// @nodoc
abstract mixin class $WalletFilterModelCopyWith<$Res>  {
  factory $WalletFilterModelCopyWith(WalletFilterModel value, $Res Function(WalletFilterModel) _then) = _$WalletFilterModelCopyWithImpl;
@useResult
$Res call({
 String? status, bool? isVerified, bool? isFlagged, double? minBalance, double? maxBalance, DateTime? startDate, DateTime? endDate
});




}
/// @nodoc
class _$WalletFilterModelCopyWithImpl<$Res>
    implements $WalletFilterModelCopyWith<$Res> {
  _$WalletFilterModelCopyWithImpl(this._self, this._then);

  final WalletFilterModel _self;
  final $Res Function(WalletFilterModel) _then;

/// Create a copy of WalletFilterModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = freezed,Object? isVerified = freezed,Object? isFlagged = freezed,Object? minBalance = freezed,Object? maxBalance = freezed,Object? startDate = freezed,Object? endDate = freezed,}) {
  return _then(_self.copyWith(
status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,isFlagged: freezed == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool?,minBalance: freezed == minBalance ? _self.minBalance : minBalance // ignore: cast_nullable_to_non_nullable
as double?,maxBalance: freezed == maxBalance ? _self.maxBalance : maxBalance // ignore: cast_nullable_to_non_nullable
as double?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [WalletFilterModel].
extension WalletFilterModelPatterns on WalletFilterModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _WalletFilterModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _WalletFilterModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _WalletFilterModel value)  $default,){
final _that = this;
switch (_that) {
case _WalletFilterModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _WalletFilterModel value)?  $default,){
final _that = this;
switch (_that) {
case _WalletFilterModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? status,  bool? isVerified,  bool? isFlagged,  double? minBalance,  double? maxBalance,  DateTime? startDate,  DateTime? endDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _WalletFilterModel() when $default != null:
return $default(_that.status,_that.isVerified,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.startDate,_that.endDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? status,  bool? isVerified,  bool? isFlagged,  double? minBalance,  double? maxBalance,  DateTime? startDate,  DateTime? endDate)  $default,) {final _that = this;
switch (_that) {
case _WalletFilterModel():
return $default(_that.status,_that.isVerified,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.startDate,_that.endDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? status,  bool? isVerified,  bool? isFlagged,  double? minBalance,  double? maxBalance,  DateTime? startDate,  DateTime? endDate)?  $default,) {final _that = this;
switch (_that) {
case _WalletFilterModel() when $default != null:
return $default(_that.status,_that.isVerified,_that.isFlagged,_that.minBalance,_that.maxBalance,_that.startDate,_that.endDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _WalletFilterModel implements WalletFilterModel {
  const _WalletFilterModel({this.status, this.isVerified, this.isFlagged, this.minBalance, this.maxBalance, this.startDate, this.endDate});
  factory _WalletFilterModel.fromJson(Map<String, dynamic> json) => _$WalletFilterModelFromJson(json);

/// Filter by wallet status
@override final  String? status;
/// Filter by verification status
@override final  bool? isVerified;
/// Filter by flagged status
@override final  bool? isFlagged;
/// Minimum balance filter
@override final  double? minBalance;
/// Maximum balance filter
@override final  double? maxBalance;
/// Start date filter
@override final  DateTime? startDate;
/// End date filter
@override final  DateTime? endDate;

/// Create a copy of WalletFilterModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WalletFilterModelCopyWith<_WalletFilterModel> get copyWith => __$WalletFilterModelCopyWithImpl<_WalletFilterModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$WalletFilterModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WalletFilterModel&&(identical(other.status, status) || other.status == status)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isFlagged, isFlagged) || other.isFlagged == isFlagged)&&(identical(other.minBalance, minBalance) || other.minBalance == minBalance)&&(identical(other.maxBalance, maxBalance) || other.maxBalance == maxBalance)&&(identical(other.startDate, startDate) || other.startDate == startDate)&&(identical(other.endDate, endDate) || other.endDate == endDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,isVerified,isFlagged,minBalance,maxBalance,startDate,endDate);

@override
String toString() {
  return 'WalletFilterModel(status: $status, isVerified: $isVerified, isFlagged: $isFlagged, minBalance: $minBalance, maxBalance: $maxBalance, startDate: $startDate, endDate: $endDate)';
}


}

/// @nodoc
abstract mixin class _$WalletFilterModelCopyWith<$Res> implements $WalletFilterModelCopyWith<$Res> {
  factory _$WalletFilterModelCopyWith(_WalletFilterModel value, $Res Function(_WalletFilterModel) _then) = __$WalletFilterModelCopyWithImpl;
@override @useResult
$Res call({
 String? status, bool? isVerified, bool? isFlagged, double? minBalance, double? maxBalance, DateTime? startDate, DateTime? endDate
});




}
/// @nodoc
class __$WalletFilterModelCopyWithImpl<$Res>
    implements _$WalletFilterModelCopyWith<$Res> {
  __$WalletFilterModelCopyWithImpl(this._self, this._then);

  final _WalletFilterModel _self;
  final $Res Function(_WalletFilterModel) _then;

/// Create a copy of WalletFilterModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = freezed,Object? isVerified = freezed,Object? isFlagged = freezed,Object? minBalance = freezed,Object? maxBalance = freezed,Object? startDate = freezed,Object? endDate = freezed,}) {
  return _then(_WalletFilterModel(
status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,isVerified: freezed == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool?,isFlagged: freezed == isFlagged ? _self.isFlagged : isFlagged // ignore: cast_nullable_to_non_nullable
as bool?,minBalance: freezed == minBalance ? _self.minBalance : minBalance // ignore: cast_nullable_to_non_nullable
as double?,maxBalance: freezed == maxBalance ? _self.maxBalance : maxBalance // ignore: cast_nullable_to_non_nullable
as double?,startDate: freezed == startDate ? _self.startDate : startDate // ignore: cast_nullable_to_non_nullable
as DateTime?,endDate: freezed == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

// dart format on
