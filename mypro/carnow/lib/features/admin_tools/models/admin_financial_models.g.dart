// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_financial_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WalletAdjustmentRequest _$WalletAdjustmentRequestFromJson(
  Map<String, dynamic> json,
) => _WalletAdjustmentRequest(
  walletId: json['wallet_id'] as String,
  adjustmentAmount: json['adjustment_amount'] as String,
  adjustmentType: json['adjustment_type'] as String,
  reason: json['reason'] as String,
  internalNotes: json['internal_notes'] as String?,
  relatedTransactionId: json['related_transaction_id'] as String?,
  adminUserId: json['admin_user_id'] as String,
);

Map<String, dynamic> _$WalletAdjustmentRequestToJson(
  _WalletAdjustmentRequest instance,
) => <String, dynamic>{
  'wallet_id': instance.walletId,
  'adjustment_amount': instance.adjustmentAmount,
  'adjustment_type': instance.adjustmentType,
  'reason': instance.reason,
  'internal_notes': instance.internalNotes,
  'related_transaction_id': instance.relatedTransactionId,
  'admin_user_id': instance.adminUserId,
};

_WalletAdjustmentResponse _$WalletAdjustmentResponseFromJson(
  Map<String, dynamic> json,
) => _WalletAdjustmentResponse(
  id: json['id'] as String,
  walletId: json['wallet_id'] as String,
  userId: json['user_id'] as String,
  adjustmentAmount: json['adjustment_amount'] as String,
  adjustmentType: json['adjustment_type'] as String,
  previousBalance: json['previous_balance'] as String,
  newBalance: json['new_balance'] as String,
  adminUserId: json['admin_user_id'] as String,
  reason: json['reason'] as String,
  internalNotes: json['internal_notes'] as String?,
  requiresApproval: json['requires_approval'] as bool,
  isApproved: json['is_approved'] as bool,
  approvedBy: json['approved_by'] as String?,
  approvedAt: json['approved_at'] == null
      ? null
      : DateTime.parse(json['approved_at'] as String),
  createdAt: DateTime.parse(json['created_at'] as String),
  processedAt: json['processed_at'] == null
      ? null
      : DateTime.parse(json['processed_at'] as String),
);

Map<String, dynamic> _$WalletAdjustmentResponseToJson(
  _WalletAdjustmentResponse instance,
) => <String, dynamic>{
  'id': instance.id,
  'wallet_id': instance.walletId,
  'user_id': instance.userId,
  'adjustment_amount': instance.adjustmentAmount,
  'adjustment_type': instance.adjustmentType,
  'previous_balance': instance.previousBalance,
  'new_balance': instance.newBalance,
  'admin_user_id': instance.adminUserId,
  'reason': instance.reason,
  'internal_notes': instance.internalNotes,
  'requires_approval': instance.requiresApproval,
  'is_approved': instance.isApproved,
  'approved_by': instance.approvedBy,
  'approved_at': instance.approvedAt?.toIso8601String(),
  'created_at': instance.createdAt.toIso8601String(),
  'processed_at': instance.processedAt?.toIso8601String(),
};

_WalletOverview _$WalletOverviewFromJson(Map<String, dynamic> json) =>
    _WalletOverview(
      walletId: json['wallet_id'] as String,
      userId: json['user_id'] as String,
      userEmail: json['user_email'] as String,
      balance: json['balance'] as String,
      currency: json['currency'] as String,
      isActive: json['is_active'] as bool,
      isLocked: json['is_locked'] as bool,
      verificationLevel: (json['verification_level'] as num).toInt(),
      verificationStatus: json['verification_status'] as String,
      dailyLimit: json['daily_limit'] as String,
      monthlyLimit: json['monthly_limit'] as String,
      canDeposit: json['can_deposit'] as bool,
      canWithdraw: json['can_withdraw'] as bool,
      canTransfer: json['can_transfer'] as bool,
      totalTransactions: (json['total_transactions'] as num).toInt(),
      totalDeposits: json['total_deposits'] as String,
      totalWithdrawals: json['total_withdrawals'] as String,
      lastActivity: json['last_activity'] == null
          ? null
          : DateTime.parse(json['last_activity'] as String),
      suspiciousActivityCount: (json['suspicious_activity_count'] as num)
          .toInt(),
      isFlagged: json['is_flagged'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$WalletOverviewToJson(_WalletOverview instance) =>
    <String, dynamic>{
      'wallet_id': instance.walletId,
      'user_id': instance.userId,
      'user_email': instance.userEmail,
      'balance': instance.balance,
      'currency': instance.currency,
      'is_active': instance.isActive,
      'is_locked': instance.isLocked,
      'verification_level': instance.verificationLevel,
      'verification_status': instance.verificationStatus,
      'daily_limit': instance.dailyLimit,
      'monthly_limit': instance.monthlyLimit,
      'can_deposit': instance.canDeposit,
      'can_withdraw': instance.canWithdraw,
      'can_transfer': instance.canTransfer,
      'total_transactions': instance.totalTransactions,
      'total_deposits': instance.totalDeposits,
      'total_withdrawals': instance.totalWithdrawals,
      'last_activity': instance.lastActivity?.toIso8601String(),
      'suspicious_activity_count': instance.suspiciousActivityCount,
      'is_flagged': instance.isFlagged,
      'created_at': instance.createdAt.toIso8601String(),
    };

_CreateAlertRequest _$CreateAlertRequestFromJson(Map<String, dynamic> json) =>
    _CreateAlertRequest(
      alertType: json['alert_type'] as String,
      severity: $enumDecode(_$AlertSeverityEnumMap, json['severity']),
      title: json['title'] as String,
      description: json['description'] as String,
      userId: json['user_id'] as String?,
      walletId: json['wallet_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      alertData: json['alert_data'] as Map<String, dynamic>?,
      thresholdValue: json['threshold_value'] as String?,
      actualValue: json['actual_value'] as String?,
    );

Map<String, dynamic> _$CreateAlertRequestToJson(_CreateAlertRequest instance) =>
    <String, dynamic>{
      'alert_type': instance.alertType,
      'severity': _$AlertSeverityEnumMap[instance.severity]!,
      'title': instance.title,
      'description': instance.description,
      'user_id': instance.userId,
      'wallet_id': instance.walletId,
      'transaction_id': instance.transactionId,
      'alert_data': instance.alertData,
      'threshold_value': instance.thresholdValue,
      'actual_value': instance.actualValue,
    };

const _$AlertSeverityEnumMap = {
  AlertSeverity.low: 'low',
  AlertSeverity.medium: 'medium',
  AlertSeverity.high: 'high',
  AlertSeverity.critical: 'critical',
};

_FinancialAlert _$FinancialAlertFromJson(Map<String, dynamic> json) =>
    _FinancialAlert(
      id: json['id'] as String,
      alertType: json['alert_type'] as String,
      severity: $enumDecode(_$AlertSeverityEnumMap, json['severity']),
      title: json['title'] as String,
      description: json['description'] as String,
      userId: json['user_id'] as String?,
      walletId: json['wallet_id'] as String?,
      transactionId: json['transaction_id'] as String?,
      alertData: json['alert_data'] as Map<String, dynamic>?,
      thresholdValue: json['threshold_value'] as String?,
      actualValue: json['actual_value'] as String?,
      isResolved: json['is_resolved'] as bool,
      resolvedBy: json['resolved_by'] as String?,
      resolutionNotes: json['resolution_notes'] as String?,
      resolvedAt: json['resolved_at'] == null
          ? null
          : DateTime.parse(json['resolved_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$FinancialAlertToJson(_FinancialAlert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'alert_type': instance.alertType,
      'severity': _$AlertSeverityEnumMap[instance.severity]!,
      'title': instance.title,
      'description': instance.description,
      'user_id': instance.userId,
      'wallet_id': instance.walletId,
      'transaction_id': instance.transactionId,
      'alert_data': instance.alertData,
      'threshold_value': instance.thresholdValue,
      'actual_value': instance.actualValue,
      'is_resolved': instance.isResolved,
      'resolved_by': instance.resolvedBy,
      'resolution_notes': instance.resolutionNotes,
      'resolved_at': instance.resolvedAt?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_ResolveAlertRequest _$ResolveAlertRequestFromJson(Map<String, dynamic> json) =>
    _ResolveAlertRequest(
      resolvedBy: json['resolved_by'] as String,
      resolutionNotes: json['resolution_notes'] as String,
    );

Map<String, dynamic> _$ResolveAlertRequestToJson(
  _ResolveAlertRequest instance,
) => <String, dynamic>{
  'resolved_by': instance.resolvedBy,
  'resolution_notes': instance.resolutionNotes,
};

_FinancialSummary _$FinancialSummaryFromJson(Map<String, dynamic> json) =>
    _FinancialSummary(
      totalWallets: (json['total_wallets'] as num).toInt(),
      activeWallets: (json['active_wallets'] as num).toInt(),
      totalPlatformBalance: json['total_platform_balance'] as String,
      totalTransactionsToday: (json['total_transactions_today'] as num).toInt(),
      totalVolumeToday: json['total_volume_today'] as String,
      newUsersToday: (json['new_users_today'] as num).toInt(),
      verifiedUsers: (json['verified_users'] as num).toInt(),
      pendingVerifications: (json['pending_verifications'] as num).toInt(),
      pendingTransactions: (json['pending_transactions'] as num).toInt(),
      failedTransactionsToday: (json['failed_transactions_today'] as num)
          .toInt(),
      refundRequests: (json['refund_requests'] as num).toInt(),
      activeAlerts: (json['active_alerts'] as num).toInt(),
      criticalAlerts: (json['critical_alerts'] as num).toInt(),
      resolvedAlertsToday: (json['resolved_alerts_today'] as num).toInt(),
      platformEarningsToday: json['platform_earnings_today'] as String,
      platformEarningsMonth: json['platform_earnings_month'] as String,
      averageCommissionRate: json['average_commission_rate'] as String,
    );

Map<String, dynamic> _$FinancialSummaryToJson(_FinancialSummary instance) =>
    <String, dynamic>{
      'total_wallets': instance.totalWallets,
      'active_wallets': instance.activeWallets,
      'total_platform_balance': instance.totalPlatformBalance,
      'total_transactions_today': instance.totalTransactionsToday,
      'total_volume_today': instance.totalVolumeToday,
      'new_users_today': instance.newUsersToday,
      'verified_users': instance.verifiedUsers,
      'pending_verifications': instance.pendingVerifications,
      'pending_transactions': instance.pendingTransactions,
      'failed_transactions_today': instance.failedTransactionsToday,
      'refund_requests': instance.refundRequests,
      'active_alerts': instance.activeAlerts,
      'critical_alerts': instance.criticalAlerts,
      'resolved_alerts_today': instance.resolvedAlertsToday,
      'platform_earnings_today': instance.platformEarningsToday,
      'platform_earnings_month': instance.platformEarningsMonth,
      'average_commission_rate': instance.averageCommissionRate,
    };

_TransactionAnalysis _$TransactionAnalysisFromJson(Map<String, dynamic> json) =>
    _TransactionAnalysis(
      transactionId: json['transaction_id'] as String,
      amount: json['amount'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      fromUserId: json['from_user_id'] as String?,
      toUserId: json['to_user_id'] as String?,
      riskScore: (json['risk_score'] as num).toDouble(),
      riskFactors: (json['risk_factors'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isSuspicious: json['is_suspicious'] as bool,
      relatedTransactions: (json['related_transactions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      timeSinceAccountCreation: (json['time_since_account_creation'] as num)
          .toInt(),
      userTransactionCount: (json['user_transaction_count'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$TransactionAnalysisToJson(
  _TransactionAnalysis instance,
) => <String, dynamic>{
  'transaction_id': instance.transactionId,
  'amount': instance.amount,
  'type': instance.type,
  'status': instance.status,
  'from_user_id': instance.fromUserId,
  'to_user_id': instance.toUserId,
  'risk_score': instance.riskScore,
  'risk_factors': instance.riskFactors,
  'is_suspicious': instance.isSuspicious,
  'related_transactions': instance.relatedTransactions,
  'time_since_account_creation': instance.timeSinceAccountCreation,
  'user_transaction_count': instance.userTransactionCount,
  'created_at': instance.createdAt.toIso8601String(),
};

_AdminActionLog _$AdminActionLogFromJson(Map<String, dynamic> json) =>
    _AdminActionLog(
      id: json['id'] as String,
      adminUserId: json['admin_user_id'] as String,
      adminEmail: json['admin_email'] as String,
      actionType: $enumDecode(_$AdminActionTypeEnumMap, json['action_type']),
      targetUserId: json['target_user_id'] as String?,
      targetWalletId: json['target_wallet_id'] as String?,
      targetTransactionId: json['target_transaction_id'] as String?,
      description: json['description'] as String,
      reason: json['reason'] as String,
      previousValue: json['previous_value'] as String?,
      newValue: json['new_value'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      ipAddress: json['ip_address'] as String?,
      userAgent: json['user_agent'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$AdminActionLogToJson(_AdminActionLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'admin_user_id': instance.adminUserId,
      'admin_email': instance.adminEmail,
      'action_type': _$AdminActionTypeEnumMap[instance.actionType]!,
      'target_user_id': instance.targetUserId,
      'target_wallet_id': instance.targetWalletId,
      'target_transaction_id': instance.targetTransactionId,
      'description': instance.description,
      'reason': instance.reason,
      'previous_value': instance.previousValue,
      'new_value': instance.newValue,
      'metadata': instance.metadata,
      'ip_address': instance.ipAddress,
      'user_agent': instance.userAgent,
      'created_at': instance.createdAt.toIso8601String(),
    };

const _$AdminActionTypeEnumMap = {
  AdminActionType.walletBalanceAdjustment: 'wallet_balance_adjustment',
  AdminActionType.transactionReversal: 'transaction_reversal',
  AdminActionType.accountFreeze: 'account_freeze',
  AdminActionType.accountUnfreeze: 'account_unfreeze',
  AdminActionType.manualRefund: 'manual_refund',
  AdminActionType.commissionAdjustment: 'commission_adjustment',
  AdminActionType.limitModification: 'limit_modification',
  AdminActionType.verificationOverride: 'verification_override',
  AdminActionType.suspiciousActivityFlag: 'suspicious_activity_flag',
  AdminActionType.reportGeneration: 'report_generation',
  AdminActionType.other: 'other',
};

_AdminDashboard _$AdminDashboardFromJson(Map<String, dynamic> json) =>
    _AdminDashboard(
      financialSummary: FinancialSummary.fromJson(
        json['financial_summary'] as Map<String, dynamic>,
      ),
      recentAlerts: (json['recent_alerts'] as List<dynamic>)
          .map((e) => FinancialAlert.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentTransactions: (json['recent_transactions'] as List<dynamic>)
          .map((e) => TransactionAnalysis.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentAdminActions: (json['recent_admin_actions'] as List<dynamic>)
          .map((e) => AdminActionLog.fromJson(e as Map<String, dynamic>))
          .toList(),
      pendingApprovals: (json['pending_approvals'] as num).toInt(),
      systemHealth: json['system_health'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$AdminDashboardToJson(_AdminDashboard instance) =>
    <String, dynamic>{
      'financial_summary': instance.financialSummary,
      'recent_alerts': instance.recentAlerts,
      'recent_transactions': instance.recentTransactions,
      'recent_admin_actions': instance.recentAdminActions,
      'pending_approvals': instance.pendingApprovals,
      'system_health': instance.systemHealth,
    };

_GenerateReportRequest _$GenerateReportRequestFromJson(
  Map<String, dynamic> json,
) => _GenerateReportRequest(
  reportType: $enumDecode(_$ReportTypeEnumMap, json['report_type']),
  title: json['title'] as String,
  description: json['description'] as String?,
  periodStart: DateTime.parse(json['period_start'] as String),
  periodEnd: DateTime.parse(json['period_end'] as String),
  generatedBy: json['generated_by'] as String,
  filters: json['filters'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$GenerateReportRequestToJson(
  _GenerateReportRequest instance,
) => <String, dynamic>{
  'report_type': _$ReportTypeEnumMap[instance.reportType]!,
  'title': instance.title,
  'description': instance.description,
  'period_start': instance.periodStart.toIso8601String(),
  'period_end': instance.periodEnd.toIso8601String(),
  'generated_by': instance.generatedBy,
  'filters': instance.filters,
};

const _$ReportTypeEnumMap = {
  ReportType.dailySummary: 'daily_summary',
  ReportType.weeklySummary: 'weekly_summary',
  ReportType.monthlySummary: 'monthly_summary',
  ReportType.transactionAnalysis: 'transaction_analysis',
  ReportType.userActivity: 'user_activity',
  ReportType.platformEarnings: 'platform_earnings',
  ReportType.refundAnalysis: 'refund_analysis',
  ReportType.suspiciousActivity: 'suspicious_activity',
  ReportType.financial: 'financial',
  ReportType.user: 'user',
  ReportType.transaction: 'transaction',
};

_AdminReport _$AdminReportFromJson(Map<String, dynamic> json) => _AdminReport(
  id: json['id'] as String,
  reportType: $enumDecode(_$ReportTypeEnumMap, json['report_type']),
  title: json['title'] as String,
  description: json['description'] as String?,
  periodStart: DateTime.parse(json['period_start'] as String),
  periodEnd: DateTime.parse(json['period_end'] as String),
  reportData: json['report_data'] as Map<String, dynamic>,
  summaryMetrics: json['summary_metrics'] as Map<String, dynamic>?,
  generatedBy: json['generated_by'] as String,
  generationTime: DateTime.parse(json['generation_time'] as String),
  filePath: json['file_path'] as String?,
  fileSize: (json['file_size'] as num?)?.toInt(),
  exportFormat: json['export_format'] as String?,
  isExported: json['is_exported'] as bool,
  exportCount: (json['export_count'] as num).toInt(),
  lastExportedAt: json['last_exported_at'] == null
      ? null
      : DateTime.parse(json['last_exported_at'] as String),
);

Map<String, dynamic> _$AdminReportToJson(_AdminReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'report_type': _$ReportTypeEnumMap[instance.reportType]!,
      'title': instance.title,
      'description': instance.description,
      'period_start': instance.periodStart.toIso8601String(),
      'period_end': instance.periodEnd.toIso8601String(),
      'report_data': instance.reportData,
      'summary_metrics': instance.summaryMetrics,
      'generated_by': instance.generatedBy,
      'generation_time': instance.generationTime.toIso8601String(),
      'file_path': instance.filePath,
      'file_size': instance.fileSize,
      'export_format': instance.exportFormat,
      'is_exported': instance.isExported,
      'export_count': instance.exportCount,
      'last_exported_at': instance.lastExportedAt?.toIso8601String(),
    };

_WalletListItem _$WalletListItemFromJson(Map<String, dynamic> json) =>
    _WalletListItem(
      walletId: json['wallet_id'] as String,
      userEmail: json['user_email'] as String,
      balance: json['balance'] as String,
      isActive: json['is_active'] as bool,
      isLocked: json['is_locked'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$WalletListItemToJson(_WalletListItem instance) =>
    <String, dynamic>{
      'wallet_id': instance.walletId,
      'user_email': instance.userEmail,
      'balance': instance.balance,
      'is_active': instance.isActive,
      'is_locked': instance.isLocked,
      'created_at': instance.createdAt.toIso8601String(),
    };

_WalletFilters _$WalletFiltersFromJson(Map<String, dynamic> json) =>
    _WalletFilters(
      status: json['status'] as String?,
      verificationLevel: json['verificationLevel'] as String?,
      isFlagged: json['isFlagged'] as bool?,
      minBalance: json['minBalance'] as String?,
      maxBalance: json['maxBalance'] as String?,
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
    );

Map<String, dynamic> _$WalletFiltersToJson(_WalletFilters instance) =>
    <String, dynamic>{
      'status': instance.status,
      'verificationLevel': instance.verificationLevel,
      'isFlagged': instance.isFlagged,
      'minBalance': instance.minBalance,
      'maxBalance': instance.maxBalance,
      'createdAfter': instance.createdAfter?.toIso8601String(),
      'createdBefore': instance.createdBefore?.toIso8601String(),
    };
