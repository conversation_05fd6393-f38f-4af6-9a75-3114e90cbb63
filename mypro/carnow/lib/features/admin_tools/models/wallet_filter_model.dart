// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_filter_model.freezed.dart';
part 'wallet_filter_model.g.dart';

/// Model for wallet filtering parameters in admin tools
@freezed
abstract class WalletFilterModel with _$WalletFilterModel {
  const factory WalletFilterModel({
    /// Filter by wallet status
    String? status,
    
    /// Filter by verification status
    bool? isVerified,
    
    /// Filter by flagged status
    bool? isFlagged,
    
    /// Minimum balance filter
    double? minBalance,
    
    /// Maximum balance filter
    double? maxBalance,
    
    /// Start date filter
    DateTime? startDate,
    
    /// End date filter
    DateTime? endDate,
  }) = _WalletFilterModel;

  factory WalletFilterModel.fromJson(Map<String, dynamic> json) =>
      _$WalletFilterModelFromJson(json);
}

/// Extension to check if any filters are active
extension WalletFilterModelExtension on WalletFilterModel {
  bool get hasActiveFilters =>
      status != null ||
      isVerified != null ||
      isFlagged != null ||
      minBalance != null ||
      maxBalance != null ||
      startDate != null ||
      endDate != null;
} 