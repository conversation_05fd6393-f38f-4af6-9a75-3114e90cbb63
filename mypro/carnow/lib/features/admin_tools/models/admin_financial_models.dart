// ignore_for_file: invalid_annotation_target

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'admin_financial_models.freezed.dart';
part 'admin_financial_models.g.dart';

/// ================================================
/// ENUMS AND TYPES
/// ================================================

enum AdminActionType {
  @JsonValue('wallet_balance_adjustment')
  walletBalanceAdjustment,
  @JsonValue('transaction_reversal')
  transactionReversal,
  @JsonValue('account_freeze')
  accountFreeze,
  @JsonValue('account_unfreeze')
  accountUnfreeze,
  @JsonValue('manual_refund')
  manualRefund,
  @JsonValue('commission_adjustment')
  commissionAdjustment,
  @JsonValue('limit_modification')
  limitModification,
  @JsonValue('verification_override')
  verificationOverride,
  @JsonValue('suspicious_activity_flag')
  suspiciousActivityFlag,
  @JsonValue('report_generation')
  reportGeneration,
  @JsonValue('other')
  other,
}

enum AlertSeverity {
  @JsonValue('low')
  low,
  @JsonValue('medium')
  medium,
  @JsonValue('high')
  high,
  @JsonValue('critical')
  critical,
}

enum ReportType {
  @JsonValue('daily_summary')
  dailySummary,
  @JsonValue('weekly_summary')
  weeklySummary,
  @JsonValue('monthly_summary')
  monthlySummary,
  @JsonValue('transaction_analysis')
  transactionAnalysis,
  @JsonValue('user_activity')
  userActivity,
  @JsonValue('platform_earnings')
  platformEarnings,
  @JsonValue('refund_analysis')
  refundAnalysis,
  @JsonValue('suspicious_activity')
  suspiciousActivity,
  @JsonValue('financial')
  financial,
  @JsonValue('user')
  user,
  @JsonValue('transaction')
  transaction,
}

enum AdminRole {
  @JsonValue('financial_analyst')
  financialAnalyst,
  @JsonValue('financial_manager')
  financialManager,
  @JsonValue('operations_manager')
  operationsManager,
  @JsonValue('senior_financial_admin')
  seniorFinancialAdmin,
  @JsonValue('compliance_officer')
  complianceOfficer,
  @JsonValue('system_administrator')
  systemAdministrator,
  @JsonValue('super_admin')
  superAdmin,
}

/// ================================================
/// WALLET MANAGEMENT MODELS
/// ================================================

@freezed
abstract class WalletAdjustmentRequest with _$WalletAdjustmentRequest {
  const factory WalletAdjustmentRequest({
    @JsonKey(name: 'wallet_id') required String walletId,
    @JsonKey(name: 'adjustment_amount') required String adjustmentAmount,
    @JsonKey(name: 'adjustment_type') required String adjustmentType,
    required String reason,
    @JsonKey(name: 'internal_notes') String? internalNotes,
    @JsonKey(name: 'related_transaction_id') String? relatedTransactionId,
    @JsonKey(name: 'admin_user_id') required String adminUserId,
  }) = _WalletAdjustmentRequest;

  factory WalletAdjustmentRequest.fromJson(Map<String, dynamic> json) =>
      _$WalletAdjustmentRequestFromJson(json);
}

@freezed
abstract class WalletAdjustmentResponse with _$WalletAdjustmentResponse {
  const factory WalletAdjustmentResponse({
    required String id,
    @JsonKey(name: 'wallet_id') required String walletId,
    @JsonKey(name: 'user_id') required String userId,
    @JsonKey(name: 'adjustment_amount') required String adjustmentAmount,
    @JsonKey(name: 'adjustment_type') required String adjustmentType,
    @JsonKey(name: 'previous_balance') required String previousBalance,
    @JsonKey(name: 'new_balance') required String newBalance,
    @JsonKey(name: 'admin_user_id') required String adminUserId,
    required String reason,
    @JsonKey(name: 'internal_notes') String? internalNotes,
    @JsonKey(name: 'requires_approval') required bool requiresApproval,
    @JsonKey(name: 'is_approved') required bool isApproved,
    @JsonKey(name: 'approved_by') String? approvedBy,
    @JsonKey(name: 'approved_at') DateTime? approvedAt,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'processed_at') DateTime? processedAt,
  }) = _WalletAdjustmentResponse;

  factory WalletAdjustmentResponse.fromJson(Map<String, dynamic> json) =>
      _$WalletAdjustmentResponseFromJson(json);
}

@freezed
abstract class WalletOverview with _$WalletOverview {
  const factory WalletOverview({
    @JsonKey(name: 'wallet_id') required String walletId,
    @JsonKey(name: 'user_id') required String userId,
    @JsonKey(name: 'user_email') required String userEmail,
    required String balance,
    required String currency,
    @JsonKey(name: 'is_active') required bool isActive,
    @JsonKey(name: 'is_locked') required bool isLocked,
    @JsonKey(name: 'verification_level') required int verificationLevel,
    @JsonKey(name: 'verification_status') required String verificationStatus,
    @JsonKey(name: 'daily_limit') required String dailyLimit,
    @JsonKey(name: 'monthly_limit') required String monthlyLimit,
    @JsonKey(name: 'can_deposit') required bool canDeposit,
    @JsonKey(name: 'can_withdraw') required bool canWithdraw,
    @JsonKey(name: 'can_transfer') required bool canTransfer,
    @JsonKey(name: 'total_transactions') required int totalTransactions,
    @JsonKey(name: 'total_deposits') required String totalDeposits,
    @JsonKey(name: 'total_withdrawals') required String totalWithdrawals,
    @JsonKey(name: 'last_activity') DateTime? lastActivity,
    @JsonKey(name: 'suspicious_activity_count')
    required int suspiciousActivityCount,
    @JsonKey(name: 'is_flagged') required bool isFlagged,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _WalletOverview;

  factory WalletOverview.fromJson(Map<String, dynamic> json) =>
      _$WalletOverviewFromJson(json);
}

/// ================================================
/// ALERT MANAGEMENT MODELS
/// ================================================

@freezed
abstract class CreateAlertRequest with _$CreateAlertRequest {
  const factory CreateAlertRequest({
    @JsonKey(name: 'alert_type') required String alertType,
    required AlertSeverity severity,
    required String title,
    required String description,
    @JsonKey(name: 'user_id') String? userId,
    @JsonKey(name: 'wallet_id') String? walletId,
    @JsonKey(name: 'transaction_id') String? transactionId,
    @JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,
    @JsonKey(name: 'threshold_value') String? thresholdValue,
    @JsonKey(name: 'actual_value') String? actualValue,
  }) = _CreateAlertRequest;

  factory CreateAlertRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateAlertRequestFromJson(json);
}

@freezed
abstract class FinancialAlert with _$FinancialAlert {
  const factory FinancialAlert({
    required String id,
    @JsonKey(name: 'alert_type') required String alertType,
    required AlertSeverity severity,
    required String title,
    required String description,
    @JsonKey(name: 'user_id') String? userId,
    @JsonKey(name: 'wallet_id') String? walletId,
    @JsonKey(name: 'transaction_id') String? transactionId,
    @JsonKey(name: 'alert_data') Map<String, dynamic>? alertData,
    @JsonKey(name: 'threshold_value') String? thresholdValue,
    @JsonKey(name: 'actual_value') String? actualValue,
    @JsonKey(name: 'is_resolved') required bool isResolved,
    @JsonKey(name: 'resolved_by') String? resolvedBy,
    @JsonKey(name: 'resolution_notes') String? resolutionNotes,
    @JsonKey(name: 'resolved_at') DateTime? resolvedAt,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _FinancialAlert;

  factory FinancialAlert.fromJson(Map<String, dynamic> json) =>
      _$FinancialAlertFromJson(json);
}

@freezed
abstract class ResolveAlertRequest with _$ResolveAlertRequest {
  const factory ResolveAlertRequest({
    @JsonKey(name: 'resolved_by') required String resolvedBy,
    @JsonKey(name: 'resolution_notes') required String resolutionNotes,
  }) = _ResolveAlertRequest;

  factory ResolveAlertRequest.fromJson(Map<String, dynamic> json) =>
      _$ResolveAlertRequestFromJson(json);
}

/// ================================================
/// FINANCIAL SUMMARY AND DASHBOARD
/// ================================================

@freezed
abstract class FinancialSummary with _$FinancialSummary {
  const factory FinancialSummary({
    @JsonKey(name: 'total_wallets') required int totalWallets,
    @JsonKey(name: 'active_wallets') required int activeWallets,
    @JsonKey(name: 'total_platform_balance')
    required String totalPlatformBalance,
    @JsonKey(name: 'total_transactions_today')
    required int totalTransactionsToday,
    @JsonKey(name: 'total_volume_today') required String totalVolumeToday,
    @JsonKey(name: 'new_users_today') required int newUsersToday,
    @JsonKey(name: 'verified_users') required int verifiedUsers,
    @JsonKey(name: 'pending_verifications') required int pendingVerifications,
    @JsonKey(name: 'pending_transactions') required int pendingTransactions,
    @JsonKey(name: 'failed_transactions_today')
    required int failedTransactionsToday,
    @JsonKey(name: 'refund_requests') required int refundRequests,
    @JsonKey(name: 'active_alerts') required int activeAlerts,
    @JsonKey(name: 'critical_alerts') required int criticalAlerts,
    @JsonKey(name: 'resolved_alerts_today') required int resolvedAlertsToday,
    @JsonKey(name: 'platform_earnings_today')
    required String platformEarningsToday,
    @JsonKey(name: 'platform_earnings_month')
    required String platformEarningsMonth,
    @JsonKey(name: 'average_commission_rate')
    required String averageCommissionRate,
  }) = _FinancialSummary;

  factory FinancialSummary.fromJson(Map<String, dynamic> json) =>
      _$FinancialSummaryFromJson(json);
}

@freezed
abstract class TransactionAnalysis with _$TransactionAnalysis {
  const factory TransactionAnalysis({
    @JsonKey(name: 'transaction_id') required String transactionId,
    required String amount,
    required String type,
    required String status,
    @JsonKey(name: 'from_user_id') String? fromUserId,
    @JsonKey(name: 'to_user_id') String? toUserId,
    @JsonKey(name: 'risk_score') required double riskScore,
    @JsonKey(name: 'risk_factors') required List<String> riskFactors,
    @JsonKey(name: 'is_suspicious') required bool isSuspicious,
    @JsonKey(name: 'related_transactions')
    required List<String> relatedTransactions,
    @JsonKey(name: 'time_since_account_creation')
    required int timeSinceAccountCreation,
    @JsonKey(name: 'user_transaction_count') required int userTransactionCount,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _TransactionAnalysis;

  factory TransactionAnalysis.fromJson(Map<String, dynamic> json) =>
      _$TransactionAnalysisFromJson(json);
}

/// ================================================
/// AUDIT LOG MODELS
/// ================================================

@freezed
abstract class AdminActionLog with _$AdminActionLog {
  const factory AdminActionLog({
    required String id,
    @JsonKey(name: 'admin_user_id') required String adminUserId,
    @JsonKey(name: 'admin_email') required String adminEmail,
    @JsonKey(name: 'action_type') required AdminActionType actionType,
    @JsonKey(name: 'target_user_id') String? targetUserId,
    @JsonKey(name: 'target_wallet_id') String? targetWalletId,
    @JsonKey(name: 'target_transaction_id') String? targetTransactionId,
    required String description,
    required String reason,
    @JsonKey(name: 'previous_value') String? previousValue,
    @JsonKey(name: 'new_value') String? newValue,
    Map<String, dynamic>? metadata,
    @JsonKey(name: 'ip_address') String? ipAddress,
    @JsonKey(name: 'user_agent') String? userAgent,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _AdminActionLog;

  factory AdminActionLog.fromJson(Map<String, dynamic> json) =>
      _$AdminActionLogFromJson(json);
}

/// ================================================
/// DASHBOARD MODELS
/// ================================================

@freezed
abstract class AdminDashboard with _$AdminDashboard {
  const factory AdminDashboard({
    @JsonKey(name: 'financial_summary')
    required FinancialSummary financialSummary,
    @JsonKey(name: 'recent_alerts') required List<FinancialAlert> recentAlerts,
    @JsonKey(name: 'recent_transactions')
    required List<TransactionAnalysis> recentTransactions,
    @JsonKey(name: 'recent_admin_actions')
    required List<AdminActionLog> recentAdminActions,
    @JsonKey(name: 'pending_approvals') required int pendingApprovals,
    @JsonKey(name: 'system_health') required Map<String, dynamic> systemHealth,
  }) = _AdminDashboard;

  factory AdminDashboard.fromJson(Map<String, dynamic> json) =>
      _$AdminDashboardFromJson(json);
}

/// ================================================
/// REPORT MODELS
/// ================================================

@freezed
abstract class GenerateReportRequest with _$GenerateReportRequest {
  const factory GenerateReportRequest({
    @JsonKey(name: 'report_type') required ReportType reportType,
    required String title,
    String? description,
    @JsonKey(name: 'period_start') required DateTime periodStart,
    @JsonKey(name: 'period_end') required DateTime periodEnd,
    @JsonKey(name: 'generated_by') required String generatedBy,
    Map<String, dynamic>? filters,
  }) = _GenerateReportRequest;

  factory GenerateReportRequest.fromJson(Map<String, dynamic> json) =>
      _$GenerateReportRequestFromJson(json);
}

@freezed
abstract class AdminReport with _$AdminReport {
  const factory AdminReport({
    required String id,
    @JsonKey(name: 'report_type') required ReportType reportType,
    required String title,
    String? description,
    @JsonKey(name: 'period_start') required DateTime periodStart,
    @JsonKey(name: 'period_end') required DateTime periodEnd,
    @JsonKey(name: 'report_data') required Map<String, dynamic> reportData,
    @JsonKey(name: 'summary_metrics') Map<String, dynamic>? summaryMetrics,
    @JsonKey(name: 'generated_by') required String generatedBy,
    @JsonKey(name: 'generation_time') required DateTime generationTime,
    @JsonKey(name: 'file_path') String? filePath,
    @JsonKey(name: 'file_size') int? fileSize,
    @JsonKey(name: 'export_format') String? exportFormat,
    @JsonKey(name: 'is_exported') required bool isExported,
    @JsonKey(name: 'export_count') required int exportCount,
    @JsonKey(name: 'last_exported_at') DateTime? lastExportedAt,
  }) = _AdminReport;

  factory AdminReport.fromJson(Map<String, dynamic> json) =>
      _$AdminReportFromJson(json);
}

/// ================================================
/// WALLET LIST ITEM (Lightweight Model for Lists)
/// ================================================

@freezed
abstract class WalletListItem with _$WalletListItem {
  const factory WalletListItem({
    @JsonKey(name: 'wallet_id') required String walletId,
    @JsonKey(name: 'user_email') required String userEmail,
    required String balance,
    @JsonKey(name: 'is_active') required bool isActive,
    @JsonKey(name: 'is_locked') required bool isLocked,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _WalletListItem;

  factory WalletListItem.fromJson(Map<String, dynamic> json) => _$WalletListItemFromJson(json);
}

/// ================================================
/// HELPER EXTENSIONS
/// ================================================

extension AlertSeverityExtension on AlertSeverity {
  String get displayName {
    switch (this) {
      case AlertSeverity.low:
        return 'منخفض';
      case AlertSeverity.medium:
        return 'متوسط';
      case AlertSeverity.high:
        return 'عالي';
      case AlertSeverity.critical:
        return 'حرج';
    }
  }

  Color get color {
    switch (this) {
      case AlertSeverity.low:
        return Colors.green;
      case AlertSeverity.medium:
        return Colors.orange;
      case AlertSeverity.high:
        return Colors.red;
      case AlertSeverity.critical:
        return Colors.deepPurple;
    }
  }
}

extension AdminActionTypeExtension on AdminActionType {
  String get displayName {
    switch (this) {
      case AdminActionType.walletBalanceAdjustment:
        return 'تعديل رصيد المحفظة';
      case AdminActionType.transactionReversal:
        return 'عكس المعاملة';
      case AdminActionType.accountFreeze:
        return 'تجميد الحساب';
      case AdminActionType.accountUnfreeze:
        return 'إلغاء تجميد الحساب';
      case AdminActionType.manualRefund:
        return 'استرداد يدوي';
      case AdminActionType.commissionAdjustment:
        return 'تعديل العمولة';
      case AdminActionType.limitModification:
        return 'تعديل الحدود';
      case AdminActionType.verificationOverride:
        return 'تجاوز التحقق';
      case AdminActionType.suspiciousActivityFlag:
        return 'تنبيه نشاط مشبوه';
      case AdminActionType.reportGeneration:
        return 'إنشاء تقرير';
      case AdminActionType.other:
        return 'آخر';
    }
  }
}

extension ReportTypeExtension on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.dailySummary:
        return 'ملخص يومي';
      case ReportType.weeklySummary:
        return 'ملخص أسبوعي';
      case ReportType.monthlySummary:
        return 'ملخص شهري';
      case ReportType.transactionAnalysis:
        return 'تحليل المعاملات';
      case ReportType.userActivity:
        return 'نشاط المستخدمين';
      case ReportType.platformEarnings:
        return 'أرباح المنصة';
      case ReportType.refundAnalysis:
        return 'تحليل المرتجعات';
      case ReportType.suspiciousActivity:
        return 'النشاط المشبوه';
      case ReportType.financial:
        return 'تقرير مالي';
      case ReportType.user:
        return 'تقرير المستخدم';
      case ReportType.transaction:
        return 'تقرير المعاملات';
    }
  }
}

/// ================================================
/// FILTERS MODEL
/// ================================================

@freezed
abstract class WalletFilters with _$WalletFilters {
  const factory WalletFilters({
    String? status,
    String? verificationLevel,
    bool? isFlagged,
    String? minBalance,
    String? maxBalance,
    DateTime? createdAfter,
    DateTime? createdBefore,
  }) = _WalletFilters;

  factory WalletFilters.fromJson(Map<String, dynamic> json) =>
      _$WalletFiltersFromJson(json);
}
