import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../admin_tools/vehicles/models/models.dart';
import '../../admin_tools/vehicles/providers/vehicle_list_providers.dart';

part 'vehicle_data_provider.g.dart';

/// Provider to get all vehicle makes for dropdowns
@riverpod
Future<List<VehicleMake>> sellerVehicleMakes(Ref ref) async {
  // Reuse the admin tools provider
  return ref.read(vehicleMakesProvider.future);
}

/// Provider to get vehicle models for a specific make
@riverpod
Future<List<VehicleModel>> sellerVehicleModels(
  Ref ref, {
  required int makeId,
}) async {
  // Reuse the admin tools provider
  return ref.read(vehicleModelsProvider(makeId: makeId).future);
}

/// Provider to get formatted vehicle names for compatibility lists
@riverpod
Future<List<String>> formattedVehicleList(Ref ref) async {
  final makes = await ref.read(sellerVehicleMakesProvider.future);
  final formattedList = <String>[];

  for (final make in makes) {
    try {
      final models = await ref.read(
        sellerVehicleModelsProvider(makeId: make.id).future,
      );

      for (final model in models) {
        // Format: "Make Model (YearStart-YearEnd)"
        final yearRange = model.yearEnd != null
            ? '${model.yearStart}-${model.yearEnd}'
            : '${model.yearStart}+';

        formattedList.add('${make.name} ${model.name} ($yearRange)');
      }
    } catch (e) {
      // Continue with next make if error
      continue;
    }
  }

  return formattedList;
}
