// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_data_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sellerVehicleMakesHash() =>
    r'bacf107bde7abe95e594e6831bca8ed1120d9269';

/// Provider to get all vehicle makes for dropdowns
///
/// Copied from [sellerVehicleMakes].
@ProviderFor(sellerVehicleMakes)
final sellerVehicleMakesProvider =
    AutoDisposeFutureProvider<List<VehicleMake>>.internal(
      sellerVehicleMakes,
      name: r'sellerVehicleMakesProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$sellerVehicleMakesHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SellerVehicleMakesRef = AutoDisposeFutureProviderRef<List<VehicleMake>>;
String _$sellerVehicleModelsHash() =>
    r'd204111a929f4d3cbce3dea35a99eb17f313c0a8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider to get vehicle models for a specific make
///
/// Copied from [sellerVehicleModels].
@ProviderFor(sellerVehicleModels)
const sellerVehicleModelsProvider = SellerVehicleModelsFamily();

/// Provider to get vehicle models for a specific make
///
/// Copied from [sellerVehicleModels].
class SellerVehicleModelsFamily extends Family<AsyncValue<List<VehicleModel>>> {
  /// Provider to get vehicle models for a specific make
  ///
  /// Copied from [sellerVehicleModels].
  const SellerVehicleModelsFamily();

  /// Provider to get vehicle models for a specific make
  ///
  /// Copied from [sellerVehicleModels].
  SellerVehicleModelsProvider call({required int makeId}) {
    return SellerVehicleModelsProvider(makeId: makeId);
  }

  @override
  SellerVehicleModelsProvider getProviderOverride(
    covariant SellerVehicleModelsProvider provider,
  ) {
    return call(makeId: provider.makeId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sellerVehicleModelsProvider';
}

/// Provider to get vehicle models for a specific make
///
/// Copied from [sellerVehicleModels].
class SellerVehicleModelsProvider
    extends AutoDisposeFutureProvider<List<VehicleModel>> {
  /// Provider to get vehicle models for a specific make
  ///
  /// Copied from [sellerVehicleModels].
  SellerVehicleModelsProvider({required int makeId})
    : this._internal(
        (ref) =>
            sellerVehicleModels(ref as SellerVehicleModelsRef, makeId: makeId),
        from: sellerVehicleModelsProvider,
        name: r'sellerVehicleModelsProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$sellerVehicleModelsHash,
        dependencies: SellerVehicleModelsFamily._dependencies,
        allTransitiveDependencies:
            SellerVehicleModelsFamily._allTransitiveDependencies,
        makeId: makeId,
      );

  SellerVehicleModelsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.makeId,
  }) : super.internal();

  final int makeId;

  @override
  Override overrideWith(
    FutureOr<List<VehicleModel>> Function(SellerVehicleModelsRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SellerVehicleModelsProvider._internal(
        (ref) => create(ref as SellerVehicleModelsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        makeId: makeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VehicleModel>> createElement() {
    return _SellerVehicleModelsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SellerVehicleModelsProvider && other.makeId == makeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, makeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SellerVehicleModelsRef
    on AutoDisposeFutureProviderRef<List<VehicleModel>> {
  /// The parameter `makeId` of this provider.
  int get makeId;
}

class _SellerVehicleModelsProviderElement
    extends AutoDisposeFutureProviderElement<List<VehicleModel>>
    with SellerVehicleModelsRef {
  _SellerVehicleModelsProviderElement(super.provider);

  @override
  int get makeId => (origin as SellerVehicleModelsProvider).makeId;
}

String _$formattedVehicleListHash() =>
    r'89dc7faf6c1b76518206ee76694b170d153c741c';

/// Provider to get formatted vehicle names for compatibility lists
///
/// Copied from [formattedVehicleList].
@ProviderFor(formattedVehicleList)
final formattedVehicleListProvider =
    AutoDisposeFutureProvider<List<String>>.internal(
      formattedVehicleList,
      name: r'formattedVehicleListProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$formattedVehicleListHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FormattedVehicleListRef = AutoDisposeFutureProviderRef<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
