/// شاشة التحليلات المبسطة (للبائع)
///
/// تقدم عرضًا مرئيًا ومبسطًا للبيانات التحليلية الهامة للبائع.
/// تستخدم رسومًا بيانية (مثل الرسوم البيانية الشريطية والدائرية) لعرض
/// مقاييس مثل المبيعات حسب الفئة، أفضل المنتجات أداءً، والإيرادات الشهرية،
/// مما يسهل فهم أداء المتجر بشكل سريع.
library;

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/app_error_widget.dart';
import '../../models/simple_analytics_model.dart';
import '../../providers/simple_analytics_provider.dart';

/// شاشة التحليلات المحسنة للبائع
class SimpleAnalyticsScreen extends ConsumerWidget {
  const SimpleAnalyticsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsAsync = ref.watch(simpleAnalyticsProvider('current_seller'));

    return Scaffold(
      appBar: AppBar(
        title: const Text('التحليلات والإحصائيات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.refresh(simpleAnalyticsProvider('current_seller')),
          ),
        ],
      ),
      body: analyticsAsync.when(
        data: (analytics) => _buildAnalyticsContent(context, analytics),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => AppErrorWidget(
          message: 'خطأ في تحميل التحليلات',
          details: error.toString(),
          onRetry: () => ref.refresh(simpleAnalyticsProvider('current_seller')),
        ),
      ),
    );
  }

  Widget _buildAnalyticsContent(
    BuildContext context,
    SimpleAnalyticsModel analytics,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تحليلات المبيعات
          _buildSalesAnalytics(context, analytics.salesData),
          const SizedBox(height: AppTheme.spacingL),

          // تحليلات المنتجات
          _buildProductAnalytics(context, analytics.productData),
          const SizedBox(height: AppTheme.spacingL),

          // تحليلات العملاء
          _buildCustomerAnalytics(context, analytics.customerData),
          const SizedBox(height: AppTheme.spacingL),

          // التوصيات
          _buildRecommendations(context, analytics.recommendations),
        ],
      ),
    );
  }

  Widget _buildSalesAnalytics(BuildContext context, SalesData sales) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'تحليلات المبيعات',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),

            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'إجمالي الإيرادات',
                    '${sales.totalRevenue.toStringAsFixed(2)} LYD',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'عدد الطلبات',
                    sales.totalOrders.toString(),
                    Icons.shopping_cart,
                    Colors.blue,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingS),

            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'متوسط قيمة الطلب',
                    '${sales.averageOrderValue.toStringAsFixed(2)} LYD',
                    Icons.calculate,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'معدل التحويل',
                    '${(sales.conversionRate * 100).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductAnalytics(BuildContext context, ProductData products) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.inventory, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'تحليلات المنتجات',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),

            // إحصائيات عامة
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'إجمالي المنتجات',
                    products.totalProducts.toString(),
                    Icons.inventory_2,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'المنتجات النشطة',
                    products.activeProducts.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingM),

            // أفضل المنتجات
            if (products.topProducts.isNotEmpty) ...[
              Text(
                'أفضل المنتجات مبيعاً',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: AppTheme.spacingS),
              ...products.topProducts
                  .take(3)
                  .map(
                    (product) => ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.blue.shade100,
                        child: Text(
                          product.orders.toString(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      title: Text(product.name),
                      subtitle: Text('${product.orders} طلب'),
                      trailing: Text(
                        '${product.revenue.toStringAsFixed(0)} LYD',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ),
            ],

            const SizedBox(height: AppTheme.spacingM),

            // تنبيهات المخزون المنخفض
            if (products.lowStockProducts.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'تنبيهات المخزون المنخفض',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ...products.lowStockProducts
                        .take(3)
                        .map(
                          (productName) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              '• $productName: مخزون منخفض',
                              style: TextStyle(color: Colors.orange.shade700),
                            ),
                          ),
                        ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerAnalytics(BuildContext context, CustomerData customers) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'تحليلات العملاء',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),

            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'عملاء جدد',
                    customers.newCustomers.toString(),
                    Icons.person_add,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'عملاء عائدين',
                    customers.returningCustomers.toString(),
                    Icons.repeat,
                    Colors.blue,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacingS),

            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'معدل الاحتفاظ',
                    '${customers.retentionRate.toStringAsFixed(1)}%',
                    Icons.favorite,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'متوسط التقييم',
                    customers.averageRating.toStringAsFixed(1),
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendations(
    BuildContext context,
    List<Recommendation> recommendations,
  ) {
    if (recommendations.isEmpty) return const SizedBox();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Colors.amber),
                const SizedBox(width: 8),
                Text('التوصيات', style: Theme.of(context).textTheme.titleLarge),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),

            ...recommendations.map(
              (recommendation) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getRecommendationColor(
                    recommendation.priority,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _getRecommendationColor(
                      recommendation.priority,
                    ).withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getRecommendationIcon(recommendation.priority),
                      color: _getRecommendationColor(recommendation.priority),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            recommendation.title,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            recommendation.description,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRecommendationColor(RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.urgent:
        return Colors.red;
      case RecommendationPriority.high:
        return Colors.orange;
      case RecommendationPriority.medium:
        return Colors.blue;
      case RecommendationPriority.low:
        return Colors.green;
    }
  }

  IconData _getRecommendationIcon(RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.urgent:
        return Icons.priority_high;
      case RecommendationPriority.high:
        return Icons.warning;
      case RecommendationPriority.medium:
        return Icons.info;
      case RecommendationPriority.low:
        return Icons.tips_and_updates;
    }
  }
}
