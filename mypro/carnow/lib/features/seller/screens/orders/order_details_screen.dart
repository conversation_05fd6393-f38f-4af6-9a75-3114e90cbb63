/// شاشة تفاصيل الطلب (للبائع)
///
/// تعرض جميع التفاصيل المتعلقة بطلب معين استلمه البائع.
/// تشمل معلومات المشتري، المنتجات المطلوبة، عنوان الشحن، والحالة الحالية للطلب.
/// تتيح للبائع تحديث حالة الطلب (مثل "قيد المعالجة"، "تم الشحن") وإضافة رقم تتبع.
library;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/order_model.dart';
import '../../../../core/models/enums.dart';
import '../../../../core/widgets/app_error_widget.dart';
import '../../providers/seller_orders_provider.dart';

class SellerOrderDetailsScreen extends HookConsumerWidget {
  const SellerOrderDetailsScreen({required this.orderId, super.key});

  final String orderId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final ordersAsync = ref.watch(sellerOrdersProvider);

    // استخدام useState لتتبع حالة الشاشة
    final isUpdating = useState<bool>(false);
    final selectedStatus = useState<OrderStatus?>(null);
    final trackingController = useTextEditingController();

    // تنسيق التاريخ والعملة
    final dateFormatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    final currencyFormatter = NumberFormat.currency(
      symbol: 'د.ل',
      decimalDigits: 3,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الطلب #$orderId'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(sellerOrdersProvider),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: ordersAsync.when(
        data: (orders) {
          // البحث عن الطلب بناءً على المعرف
          final orderIdInt = int.tryParse(orderId);
          final order = orders.firstWhere(
            (OrderModel order) => order.id == orderIdInt,
            orElse: () => const OrderModel(),
          );

          if (order.id == null) {
            return const Center(child: Text('لم يتم العثور على الطلب'));
          }

          // تحديث حالة الاختيار الأولية
          if (selectedStatus.value == null && order.status != null) {
            selectedStatus.value = order.status;
          }

          return RefreshIndicator(
            onRefresh: () async => ref.invalidate(sellerOrdersProvider),
            child: ListView(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              children: [
                // بطاقة معلومات الطلب
                _buildOrderInfoCard(
                  context,
                  order,
                  dateFormatter,
                  currencyFormatter,
                  theme,
                ),

                const SizedBox(height: AppTheme.spacingM),

                // بطاقة عنوان الشحن
                _buildShippingAddressCard(context, order, theme),

                const SizedBox(height: AppTheme.spacingM),

                // بطاقة عناصر الطلب
                _buildOrderItemsCard(context, order, currencyFormatter, theme),

                const SizedBox(height: AppTheme.spacingM),

                // بطاقة تحديث الحالة
                _buildUpdateStatusCard(
                  context,
                  order,
                  selectedStatus.value,
                  (status) => selectedStatus.value = status,
                  trackingController,
                  isUpdating.value,
                  () async {
                    if (selectedStatus.value == null ||
                        selectedStatus.value == order.status) {
                      return;
                    }

                    isUpdating.value = true;

                    try {
                      await ref
                          .read(sellerOrdersProvider.notifier)
                          .updateOrderStatus(order.id!, selectedStatus.value!);

                      // إذا تم اختيار حالة الشحن، أضف رقم التتبع
                      if (selectedStatus.value == OrderStatus.shipped &&
                          trackingController.text.isNotEmpty) {
                        await ref
                            .read(sellerOrdersProvider.notifier)
                            .updateTrackingNumber(
                              order.id!,
                              trackingController.text,
                            );
                      }

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث حالة الطلب بنجاح'),
                          ),
                        );
                      }
                    } catch (e) {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('فشل تحديث حالة الطلب: $e')),
                        );
                      }
                    } finally {
                      isUpdating.value = false;
                    }
                  },
                  theme,
                ),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => AppErrorWidget(
          message: 'فشل في تحميل تفاصيل الطلب',
          details: err.toString(),
          stackTrace: stack,
          onRetry: () => ref.invalidate(sellerOrdersProvider),
        ),
      ),
    );
  }

  // بناء بطاقة معلومات الطلب
  Widget _buildOrderInfoCard(
    BuildContext context,
    OrderModel order,
    DateFormat dateFormatter,
    NumberFormat currencyFormatter,
    ThemeData theme,
  ) => Card(
    child: Padding(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الطلب',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          _buildInfoRow('رقم الطلب', '#${order.id}', theme),
          _buildInfoRow(
            'تاريخ الطلب',
            dateFormatter.format(order.createdAt ?? DateTime.now()),
            theme,
          ),
          _buildInfoRow(
            'حالة الطلب',
            _getStatusName(order.status),
            theme,
            valueColor: _getStatusColor(order.status),
          ),
          _buildInfoRow(
            'المبلغ الإجمالي',
            currencyFormatter.format(order.totalAmount ?? 0),
            theme,
            valueColor: theme.colorScheme.primary,
          ),
          _buildInfoRow('رقم المشتري', '${order.buyerId ?? ''}', theme),
          if (order.status == OrderStatus.shipped &&
              order.trackingNumber != null)
            _buildInfoRow('رقم التتبع', order.trackingNumber!, theme),
        ],
      ),
    ),
  );

  // بناء بطاقة عنوان الشحن
  Widget _buildShippingAddressCard(
    BuildContext context,
    OrderModel order,
    ThemeData theme,
  ) => Card(
    child: Padding(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عنوان الشحن',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            order.shippingAddress ?? 'لا يوجد عنوان شحن',
            style: theme.textTheme.bodyLarge,
          ),
        ],
      ),
    ),
  );

  // بناء بطاقة عناصر الطلب
  Widget _buildOrderItemsCard(
    BuildContext context,
    OrderModel order,
    NumberFormat currencyFormatter,
    ThemeData theme,
  ) {
    final items = order.items ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عناصر الطلب',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingM),
            if (items.isEmpty)
              const Text('لا توجد عناصر في هذا الطلب')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: items.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final item = items[index];
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${item.quantity ?? 0} x منتج رقم ${item.productId ?? ''}',
                        style: theme.textTheme.bodyLarge,
                      ),
                      Text(
                        currencyFormatter.format(
                          (item.unitPrice ?? 0) * (item.quantity ?? 0),
                        ),
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة تحديث الحالة
  Widget _buildUpdateStatusCard(
    BuildContext context,
    OrderModel order,
    OrderStatus? selectedStatus,
    void Function(OrderStatus? status) onStatusChanged,
    TextEditingController trackingController,
    bool isUpdating,
    VoidCallback onUpdate,
    ThemeData theme,
  ) => Card(
    child: Padding(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تحديث حالة الطلب',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          DropdownButtonFormField<OrderStatus>(
            value: selectedStatus,
            items: OrderStatus.values
                .map(
                  (status) => DropdownMenuItem(
                    value: status,
                    child: Text(_getStatusName(status)),
                  ),
                )
                .toList(),
            onChanged: (status) {
              if (status != null) {
                onStatusChanged(status);
              }
            },
            decoration: const InputDecoration(
              labelText: 'الحالة الجديدة',
              border: OutlineInputBorder(),
            ),
          ),

          // إظهار حقل رقم التتبع إذا كانت الحالة هي "تم الشحن"
          if (selectedStatus == OrderStatus.shipped) ...[
            const SizedBox(height: AppTheme.spacingM),
            TextField(
              controller: trackingController,
              decoration: const InputDecoration(
                labelText: 'رقم تتبع الشحنة',
                hintText: 'أدخل رقم تتبع الشحنة',
                border: OutlineInputBorder(),
              ),
            ),
          ],

          const SizedBox(height: AppTheme.spacingL),

          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: isUpdating ? null : onUpdate,
              child: isUpdating
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('تحديث حالة الطلب'),
            ),
          ),
        ],
      ),
    ),
  );

  // بناء صف معلومات
  Widget _buildInfoRow(
    String label,
    String value,
    ThemeData theme, {
    Color? valueColor,
  }) => Padding(
    padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('$label:', style: theme.textTheme.bodyLarge),
        Flexible(
          child: Text(
            value,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    ),
  );

  // الحصول على اسم الحالة باللغة العربية
  String _getStatusName(OrderStatus? status) {
    switch (status) {
      case OrderStatus.pending:
        return 'قيد المراجعة';
      case OrderStatus.processing:
        return 'قيد التجهيز';
      case OrderStatus.confirmed:
        return 'تم التأكيد';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التوصيل';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'تم الإرجاع';
      case OrderStatus.refunded:
        return 'تم استرداد المبلغ';
      default:
        return 'غير معروف';
    }
  }

  // الحصول على لون الحالة
  Color _getStatusColor(OrderStatus? status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.processing:
        return Colors.blueGrey;
      case OrderStatus.confirmed:
        return Colors.blue;
      case OrderStatus.shipped:
        return Colors.purple;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.red;
      case OrderStatus.returned:
        return Colors.brown;
      case OrderStatus.refunded:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
