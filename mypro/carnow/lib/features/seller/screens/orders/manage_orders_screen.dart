/// شاشة إدارة الطلبات (للبائع)
///
/// تعرض قائمة بجميع الطلبات التي استلمها البائع.
/// توفر أدوات لتصفية الطلبات حسب حالتها (مثل "قيد الانتظار"، "مؤكد"، "ملغي")
/// مما يسهل على البائع متابعة وإدارة جميع الطلبات بكفاءة.
library;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/order_model.dart';
import '../../../../core/models/enums.dart';
import '../../../../core/widgets/app_error_widget.dart';
import '../../providers/seller_orders_provider.dart';

// تهيئة منسق التاريخ والعملة خارج دالة البناء لتجنب إعادة الإنشاء
final _dateFormatter = DateFormat('dd/MM/yyyy HH:mm', 'ar');
final _currencyFormatter = NumberFormat.currency(
  symbol: 'د.ل', // رمز الدينار الليبي
  decimalDigits: 3, // الدينار الليبي يستخدم 3 خانات عشرية
);

class SellerOrdersManagementScreen extends HookConsumerWidget {
  const SellerOrdersManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final filteredOrdersAsync = ref.watch(filteredOrdersProvider);
    // إعادة statusFilter هنا ليظل متاحًا في كل النطاق
    final statusFilter = ref.watch(orderStatusFilterProvider);

    // عناصر الترشيح
    final filterItems = <DropdownMenuItem<OrderStatus?>>[
      const DropdownMenuItem<OrderStatus?>(child: Text('كل الطلبات')),
      ...OrderStatus.values.map(
        (status) => DropdownMenuItem<OrderStatus?>(
          value: status,
          child: Text(_getOrderStatusName(status)),
        ),
      ),
    ];

    // قائمة تظهر فقط للتحميل الأولي
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الطلبات'),
        actions: [
          // استخدام Consumer لإعادة بناء الزر فقط عند الحاجة
          Consumer(
            builder: (context, ref, child) {
              return IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () =>
                    ref.read(sellerOrdersProvider.notifier).refresh(),
                tooltip: 'تحديث',
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // عنصر الترشيح
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Row(
              children: [
                Text('التصفية حسب: ', style: theme.textTheme.bodyLarge),
                const SizedBox(width: 8),
                Expanded(
                  // استخدام Consumer لإعادة بناء القائمة المنسدلة فقط
                  child: Consumer(
                    builder: (context, ref, child) {
                      // لا حاجة لـ watch هنا لأنه موجود في النطاق الأعلى
                      return DropdownButton<OrderStatus?>(
                        value: statusFilter,
                        isExpanded: true,
                        hint: const Text('اختر الحالة'),
                        items: filterItems,
                        onChanged: (newValue) {
                          ref
                              .read(orderStatusFilterProvider.notifier)
                              .setFilter(newValue);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // عنصر نتائج الترشيح العليا
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingM,
              vertical: AppTheme.spacingS,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  // سيتم تحديثه بعدد الطلبات عند جلب البيانات
                  filteredOrdersAsync.maybeWhen(
                    data: (orders) => 'الطلبات: ${orders.length}',
                    orElse: () => 'الطلبات',
                  ),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // قائمة الطلبات
          Expanded(
            child: filteredOrdersAsync.when(
              data: (orders) {
                if (orders.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inbox_outlined,
                          size: 64,
                          color: theme.colorScheme.outline,
                        ),
                        const SizedBox(height: AppTheme.spacingM),
                        Text(
                          statusFilter == null
                              ? 'لم يتم العثور على طلبات'
                              : 'لا توجد طلبات بحالة ${_getOrderStatusName(statusFilter)}',
                          style: theme.textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.separated(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  itemCount: orders.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: AppTheme.spacingS),
                  itemBuilder: (context, index) {
                    final order = orders[index];
                    return _OrderCard(
                      order: order,
                      dateFormatter: _dateFormatter,
                      currencyFormatter: _currencyFormatter,
                      onTap: () => _navigateToOrderDetails(context, order),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (err, stack) => AppErrorWidget(
                message: 'فشل في تحميل الطلبات',
                details: err.toString(),
                stackTrace: stack,
                onRetry: () => ref.invalidate(filteredOrdersProvider),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // الانتقال إلى تفاصيل الطلب
  void _navigateToOrderDetails(BuildContext context, OrderModel order) {
    context.push('/seller/orders/manage/${order.id}');
  }

  // الحصول على اسم حالة الطلب بالعربية
  String _getOrderStatusName(OrderStatus? status) {
    if (status == null) return 'الكل';

    switch (status) {
      case OrderStatus.pending:
        return 'قيد الانتظار';
      case OrderStatus.processing:
        return 'قيد المعالجة';
      case OrderStatus.confirmed:
        return 'مؤكد';
      case OrderStatus.shipped:
        return 'تم الشحن';
      case OrderStatus.delivered:
        return 'تم التسليم';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.returned:
        return 'مرتجع';
      case OrderStatus.refunded:
        return 'مسترد';
    }
  }
}

// بطاقة عرض الطلب في القائمة
class _OrderCard extends StatelessWidget {
  const _OrderCard({
    required this.order,
    required this.dateFormatter,
    required this.currencyFormatter,
    required this.onTap,
  });
  final OrderModel order;
  final DateFormat dateFormatter;
  final NumberFormat currencyFormatter;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // الحصول على لون حالة الطلب
    Color statusColor;
    String statusText;

    if (order.status == null) {
      statusColor = Colors.grey;
      statusText = 'غير معروف';
    } else {
      switch (order.status!) {
        case OrderStatus.pending:
          statusColor = Colors.orange;
          statusText = 'قيد الانتظار';
          break;
        case OrderStatus.processing:
          statusColor = Colors.blueGrey;
          statusText = 'قيد المعالجة';
          break;
        case OrderStatus.confirmed:
          statusColor = Colors.blue;
          statusText = 'مؤكد';
          break;
        case OrderStatus.shipped:
          statusColor = Colors.purple;
          statusText = 'تم الشحن';
          break;
        case OrderStatus.delivered:
          statusColor = Colors.green;
          statusText = 'تم التسليم';
          break;
        case OrderStatus.cancelled:
          statusColor = Colors.red;
          statusText = 'ملغي';
          break;
        case OrderStatus.returned:
          statusColor = Colors.deepOrange;
          statusText = 'مرتجع';
          break;
        case OrderStatus.refunded:
          statusColor = Colors.brown;
          statusText = 'مسترد';
          break;
      }
    }

    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة مع المعرف والتاريخ
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'رقم الطلب: ${order.id}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    dateFormatter.format(order.createdAt ?? DateTime.now()),
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingS),

              // معلومات المشتري والسعر
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المشتري: ${order.buyerId ?? ''}',
                    style: theme.textTheme.bodyMedium,
                  ),
                  Text(
                    currencyFormatter.format(order.totalAmount ?? 0),
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingS),

              // عناصر الطلب
              if (order.items != null && order.items!.isNotEmpty)
                Text(
                  'العناصر: ${order.items!.length}',
                  style: theme.textTheme.bodyMedium,
                ),
              const SizedBox(height: AppTheme.spacingM),

              // رقاقة الحالة
              Align(
                alignment: Alignment.bottomRight,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingM,
                    vertical: AppTheme.spacingS / 2,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(
                      red: statusColor.r,
                      green: statusColor.g,
                      blue: statusColor.b,
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
