import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/providers/users_provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/feature_list_item.dart';
import '../../../shared/widgets/quick_action_card.dart';
import '../../../l10n/app_localizations.dart';
import '../models/user_model.dart';

/// شاشة MyCarNow (حسابي)
///
/// تُشكّل لوحة تحكم شخصية للمستخدم تحتوي على معلومات الحساب، إجراءات
/// سريعة، والانتقال إلى الميزات الرئيسة مثل الطلبات، السلة، الإشعارات،
/// والكراج الذكي.
class MyCarNowScreen extends HookConsumerWidget {
  const MyCarNowScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final userProfileAsync = ref.watch(currentUserStreamProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.myCarNow),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => context.push('/notifications'),
          ),
          IconButton(
            icon: const Icon(Icons.shopping_cart_outlined),
            onPressed: () => context.push('/cart'),
          ),
        ],
      ),
      body: userProfileAsync.when(
        data: (userProfile) => userProfile == null 
          ? _buildUnauthenticatedContent(context, l10n)
          : _buildAuthenticatedContent(context, l10n, userProfile),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildUnauthenticatedContent(context, l10n),
      ),
    );
  }

  Widget _buildAuthenticatedContent(
    BuildContext context,
    AppLocalizations l10n,
    UserModel user,
  ) => SingleChildScrollView(
    child: Column(
      children: [
        // User Profile Header
        _buildUserProfileHeader(context, l10n, user),

        const SizedBox(height: 16),

        // Quick Actions Section
        _buildQuickActionsSection(context, l10n),

        const SizedBox(height: 24),

        // Main Features Section
        _buildMainFeaturesSection(context, l10n),

        const SizedBox(height: 24),

        // Additional Services Section
        _buildAdditionalServicesSection(context, l10n),
      ],
    ),
  );

  Widget _buildUnauthenticatedContent(
    BuildContext context,
    AppLocalizations l10n,
  ) => Center(
    child: Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 100,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 24),
          Text(
            l10n.welcomeToCarNow,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            l10n.signInToAccessAccount,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: () => context.push('/login'),
              style: FilledButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                l10n.signIn,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => context.push('/register'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                l10n.createAccount,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildUserProfileHeader(
    BuildContext context,
    AppLocalizations l10n,
    UserModel user,
  ) => Container(
    width: double.infinity,
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          AppColors.primary,
          AppColors.primary.withAlpha((0.80 * 255).toInt()),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ),
    child: Row(
      children: [
        CircleAvatar(
          radius: 30,
          backgroundColor: Colors.white,
          child: Text(
            user.name?.substring(0, 1).toUpperCase() ?? 'U',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user.name ?? l10n.carNowUser,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${l10n.memberSince} '
                '${_getJoinDate(user.createdAt?.toIso8601String())}',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ],
          ),
        ),
        const Icon(Icons.chevron_right, color: Colors.white, size: 28),
      ],
    ),
  );

  Widget _buildQuickActionsSection(
    BuildContext context,
    AppLocalizations l10n,
  ) => Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.yourUpdates,
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                title: l10n.watchlist,
                subtitle: l10n.favoritesAndLists,
                icon: Icons.favorite_outline,
                onTap: () => context.push('/favorites'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickActionCard(
                title: l10n.bidsAndOffers,
                subtitle: l10n.activeAuctionsAndOffers,
                icon: Icons.local_offer_outlined,
                onTap: () => context.push('/auctions'),
              ),
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildMainFeaturesSection(
    BuildContext context,
    AppLocalizations l10n,
  ) => Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.shopping,
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Column(
          children: [
            FeatureListItem(
              icon: Icons.favorite_outline,
              title: l10n.watchlist,
              onTap: () => context.push('/favorites'),
            ),
            FeatureListItem(
              icon: Icons.bookmark_outline,
              title: l10n.savedItems,
              onTap: () => context.push('/saved'),
            ),
            FeatureListItem(
              icon: Icons.refresh,
              title: l10n.buyAgain,
              onTap: () => context.push('/history/purchases'),
            ),
            FeatureListItem(
              icon: Icons.shopping_bag_outlined,
              title: l10n.purchases,
              onTap: () => context.push('/orders'),
            ),
            FeatureListItem(
              icon: Icons.gavel_outlined,
              title: l10n.bidsAndOffers,
              onTap: () => context.push('/auctions'),
            ),
            FeatureListItem(
              icon: Icons.visibility_outlined,
              title: l10n.recentlyViewed,
              onTap: () => context.push('/history/viewed'),
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildAdditionalServicesSection(
    BuildContext context,
    AppLocalizations l10n,
  ) => Container(
    margin: const EdgeInsets.symmetric(horizontal: 16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.blue.shade50, Colors.blue.shade100],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(16),
      border: Border.all(color: Colors.blue.shade200),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.garage, color: Colors.blue.shade700, size: 32),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.carNowGarage,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    l10n.addCarGetCompatibleParts,
                    style: TextStyle(color: Colors.blue.shade600, fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: () => context.push('/garage'),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(l10n.manageGarage),
          ),
        ),
      ],
    ),
  );

  String _getJoinDate(String? createdAt) {
    if (createdAt == null) {
      return '2024';
    }

    try {
      final date = DateTime.parse(createdAt);
      return '${date.year}';
    } catch (e) {
      return '2024';
    }
  }
}
