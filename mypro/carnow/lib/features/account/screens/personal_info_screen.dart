import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../l10n/app_localizations.dart';

/// شاشة المعلومات الشخصية
///
/// تتيح للمستخدم عرض معلوماته الشخصية وتحديثها مثل الاسم، البريد
/// الإلكتروني، ورقم الهاتف.
class PersonalInfoScreen extends HookConsumerWidget {
  const PersonalInfoScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.personalInformation,
        ), // Assuming this key exists or will be added
      ),
      body: Center(child: Text(l10n.personalInformation)),
    );
  }
}
