import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:carnow/l10n/app_localizations.dart';

/// شاشة تتطلب تسجيل الدخول
///
/// تُعرض عندما يحاول المستخدم الوصول إلى مسار يتطلب المصادقة دون أن يكون
/// قد سجّل دخوله. تقدّم رسالة توعوية وزراً للتنقل إلى صفحة تسجيل الدخول
/// أو إنشاء حساب جديد، مع الاحتفاظ بالوجهة الأصلية لإعادة التوجيه بعد
/// النجاح.
class LoginRequiredScreen extends ConsumerWidget {
  const LoginRequiredScreen({super.key, this.from});

  /// The route the user was trying to access before being redirected.
  final String? from;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(title: Text(l10n.loginRequiredTitle)),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.lock_outline, size: 80, color: Colors.amber),
              const SizedBox(height: 24),
              Text(
                l10n.loginRequiredHeading,
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                l10n.loginRequiredMessage,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              FilledButton(
                style: FilledButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                ),
                onPressed: () {
                  // Navigate to login, and pass the 'from' so we can return
                  // to the original destination after successful login.
                  context.push('/login?from=${from ?? '/'}');
                },
                child: Text(l10n.loginButton),
              ),
              const SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  context.push('/register?from=${from ?? '/'}');
                },
                child: Text(l10n.createAccountButton),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
