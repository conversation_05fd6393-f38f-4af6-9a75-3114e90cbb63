import 'package:flutter/material.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';

class AccountListTile extends StatelessWidget {
  const AccountListTile({
    required this.title,
    required this.subtitle,
    required this.icon,
    super.key,
    this.onTap,
    this.isDestructive = false,
  });

  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = isDestructive
        ? theme.colorScheme.error
        : theme.colorScheme.onSurface;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('AccountListTile tapped: $title');
          onTap?.call();
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: color.withAlpha((255 * 0.1).round()),
              foregroundColor: color,
              child: Icon(icon, size: 20),
            ),
            title: Text(
              title,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: color.withAlpha((255 * 0.7).round()),
              ),
            ),
            trailing: isDestructive
                ? null
                : const Icon(Ionicons.chevron_forward, size: 16),
          ),
        ),
      ),
    );
  }
}
