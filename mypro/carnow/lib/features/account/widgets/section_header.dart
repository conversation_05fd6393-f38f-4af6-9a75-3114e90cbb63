import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

class SectionHeader extends StatelessWidget {
  const SectionHeader({required this.title, super.key, this.trailing});

  final String title;
  final Widget? trailing;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppTheme.spacingS,
        left: AppTheme.spacingS,
        right: AppTheme.spacingS,
      ),
      child: Text(title, style: textTheme.titleLarge),
    );
  }
}
