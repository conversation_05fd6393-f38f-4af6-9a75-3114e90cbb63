_fe_analyzer_shared
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-85.0.0/lib/
analyzer
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.6.0/lib/
analyzer_plugin
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer_plugin-0.13.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer_plugin-0.13.4/lib/
app_links
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib/
app_links_linux
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/
app_links_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/
app_links_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/
barcode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/lib/
bidi
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/lib/
build_config
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/lib/
build_daemon
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/lib/
build_resolvers
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/lib/
build_runner
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/lib/
build_runner_core
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.11.0/lib/
cached_network_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/lib/
cli_config
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_config-0.2.0/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/
connectivity_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
coverage
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.15.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.15.0/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csv
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csv-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csv-6.0.0/lib/
custom_lint_core
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_core-0.7.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_core-0.7.5/lib/
custom_lint_visitor
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_visitor-1.0.0+7.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_lint_visitor-1.0.0+7.7.0/lib/
dart_style
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.1/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
dio
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
dispose_scope
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dispose_scope-2.1.0/lib/
dynamic_color
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dynamic_color-1.7.0/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-10.2.0/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/lib/
flutter_cache_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_dotenv
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/
flutter_hooks
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_hooks-0.21.2/lib/
flutter_lints
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/
flutter_secure_storage_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib/
flutter_secure_storage_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib/
flutter_secure_storage_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/
flutter_secure_storage_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib/
flutter_secure_storage_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/
flutter_svg
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib/
flutter_vector_icons
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_vector_icons-2.0.0/lib/
freezed
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed-3.1.0/lib/
freezed_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.1.0/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
functions_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/
gap
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gap-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gap-3.0.1/lib/
geoclue
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geoclue-0.1.1/lib/
geolocator
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-14.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-14.0.2/lib/
geolocator_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/lib/
geolocator_apple
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/
geolocator_linux
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_linux-0.2.3/lib/
geolocator_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/
geolocator_web
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/lib/
geolocator_windows
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
go_router
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/
google_identity_services_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_sign_in
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-7.1.1/lib/
google_sign_in_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-7.0.3/lib/
google_sign_in_ios
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-6.1.0/lib/
google_sign_in_platform_interface
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-3.0.0/lib/
google_sign_in_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-1.0.0/lib/
gotrue
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
gsettings
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gsettings-0.2.8/lib/
gtk
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/
hive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/
hive_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/
hooks_riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hooks_riverpod-2.6.1/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib/
image_picker_for_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
jni
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jni-0.14.1/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
json_serializable
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.5/lib/
jwt_decode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/lib/
local_auth
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib/
local_auth_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/lib/
local_auth_darwin
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/lib/
local_auth_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib/
local_auth_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib/
logger
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.1/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
mailer
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mailer-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mailer-6.5.0/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.13.0/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/
mobile_scanner
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-7.0.1/lib/
mockito
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.6/lib/
mocktail
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
node_preamble
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/lib/
octo_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
package_info_plus
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
patrol
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol-3.18.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol-3.18.0/lib/
patrol_finders
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_finders-2.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_finders-2.9.0/lib/
patrol_log
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_log-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/patrol_log-0.5.0/lib/
pdf
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/lib/
postgrest
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/lib/
qr
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/lib/
qr_flutter
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/lib/
realtime_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/
retry
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/lib/
riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/
riverpod_analyzer_utils
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_analyzer_utils-0.5.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_analyzer_utils-0.5.10/lib/
riverpod_annotation
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_annotation-2.6.1/lib/
riverpod_generator
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_generator-2.6.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod_generator-2.6.5/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/
sentry
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry-9.5.0/lib/
sentry_flutter
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-9.5.0/lib/
share_plus
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/lib/
share_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-6.0.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/lib/
shelf_packages_handler
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/lib/
shelf_static
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.3/lib/
shelf_web_socket
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/lib/
shimmer
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib/
smooth_page_indicator
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/smooth_page_indicator-1.2.1/lib/
source_gen
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0/lib/
source_helper
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.6/lib/
source_map_stack_trace
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.2/lib/
source_maps
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.13/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/
sqflite_common_ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common_ffi-2.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common_ffi-2.3.6/lib/
sqflite_darwin
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
sqlite3
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.8.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
state_notifier
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/
storage_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
supabase
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/
supabase_flutter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/
syncfusion_flutter_charts
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-30.1.42/lib/
syncfusion_flutter_core
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-30.1.42/lib/
synchronized
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
test_core
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_graphics
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib/
vector_graphics_codec
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib/
vector_graphics_compiler
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
webkit_inspection_protocol
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/lib/
win32
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
yet_another_json_isolate
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib/
sky_engine
3.7
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/development/flutter/packages/flutter/
file:///Users/<USER>/development/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/development/flutter/packages/flutter_localizations/
file:///Users/<USER>/development/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/development/flutter/packages/flutter_test/
file:///Users/<USER>/development/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/lib/
carnow
3.8
file:///Users/<USER>/mypro/carnow/
file:///Users/<USER>/mypro/carnow/lib/
2
